#!/usr/bin/env python3
"""
Advanced configuration examples for DocuMorph
"""

import yaml
from pathlib import Path
from documorph.core.document_parser import DocumentParser
from documorph.core.content_processor import ContentProcessor
from documorph.core.output_generator import OutputGenerator
from config.config_manager import config_manager


def create_custom_config():
    """Create a custom configuration file"""
    print("=== Creating Custom Configuration ===")
    
    custom_config = {
        'output': {
            'html': {
                'responsive': True,
                'theme': 'dark',
                'include_toc': True,
                'dark_mode': True,
                'font_family': 'Roboto, sans-serif',
                'max_width': '1000px'
            },
            'quiz': {
                'question_types': ['multiple_choice', 'true_false', 'short_answer'],
                'difficulty_levels': ['easy', 'medium', 'hard'],
                'questions_per_section': 7,
                'randomize_questions': True,
                'show_explanations': True,
                'time_limit': 600
            },
            'audio': {
                'voice': 'en-us',
                'speed': 1.2,
                'format': 'mp3',
                'quality': 'high',
                'add_intro': True,
                'add_outro': True,
                'chapter_breaks': True
            },
            'translation': {
                'preserve_formatting': True,
                'target_languages': ['es', 'fr', 'de', 'it', 'pt', 'ru'],
                'detect_source_language': True,
                'translate_metadata': True
            }
        },
        'processing': {
            'max_file_size': '100MB',
            'supported_formats': ['pdf', 'docx', 'txt', 'md'],
            'output_quality': 'high',
            'parallel_processing': True,
            'max_workers': 6,
            'temp_dir': './temp',
            'min_section_length': 150,
            'max_summary_length': 750
        },
        'apis': {
            'google_translate': {
                'api_key': '${GOOGLE_TRANSLATE_API_KEY}',
                'rate_limit': 200,
                'timeout': 45,
                'retry_attempts': 5
            },
            'tts_service': {
                'provider': 'gtts',
                'rate_limit': 75,
                'timeout': 90,
                'chunk_size': 1500
            }
        },
        'logging': {
            'level': 'DEBUG',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file': 'logs/documorph_advanced.log',
            'max_size': '25MB',
            'backup_count': 10
        },
        'security': {
            'max_content_length': 104857600,  # 100MB
            'allowed_extensions': ['.pdf', '.docx', '.txt', '.md'],
            'sanitize_filenames': True,
            'validate_input': True
        },
        'performance': {
            'cache_enabled': True,
            'cache_dir': './cache',
            'cache_ttl': 7200,  # 2 hours
            'memory_limit': '2GB'
        },
        'features': {
            'experimental_parsers': True,
            'advanced_quiz_types': True,
            'batch_processing': True,
            'web_preview': True
        }
    }
    
    # Save configuration
    config_file = Path('advanced_config.yaml')
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(custom_config, f, default_flow_style=False, indent=2)
    
    print(f"Custom configuration saved to: {config_file}")
    return config_file


def demonstrate_config_usage():
    """Demonstrate using custom configuration"""
    print("\n=== Using Custom Configuration ===")
    
    # Create custom config
    config_file = create_custom_config()
    
    try:
        # Load custom configuration
        config_manager.load_user_config(str(config_file))
        
        # Show some configuration values
        print("\nCustom configuration loaded:")
        print(f"- HTML theme: {config_manager.get('output.html.theme')}")
        print(f"- Quiz questions per section: {config_manager.get('output.quiz.questions_per_section')}")
        print(f"- Audio speed: {config_manager.get('output.audio.speed')}")
        print(f"- Max file size: {config_manager.get('processing.max_file_size')}")
        print(f"- Log level: {config_manager.get('logging.level')}")
        
        # Create sample document for testing
        sample_content = """# Advanced Configuration Test

This document tests advanced configuration features.

## Performance Settings

The system is configured for high performance with:
- Parallel processing enabled
- Increased worker count
- Extended cache TTL
- Higher memory limits

## Output Customization

Custom output settings include:
- Dark theme for HTML
- Extended quiz time limits
- Faster audio playback speed
- Multiple translation languages

## Security Features

Enhanced security includes:
- Input validation
- Filename sanitization
- Content length limits
- Extension restrictions
"""
        
        # Save sample document
        sample_file = Path('advanced_test.md')
        with open(sample_file, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        # Process with custom configuration
        print(f"\nProcessing sample document with custom config...")
        
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        
        # Parse and process
        parsed_doc = parser.parse(str(sample_file))
        processed_content = processor.process(parsed_doc)
        
        print(f"- Parsed document: {parsed_doc.format}")
        print(f"- Sections found: {len(processed_content.sections)}")
        print(f"- Reading time: {processed_content.reading_time} minutes")
        print(f"- Difficulty: {processed_content.difficulty_level}")
        
        # Generate outputs with custom settings
        print(f"\nGenerating outputs with custom settings...")
        
        # HTML with dark theme
        html_output = generator.generate(
            processed_content, 
            'html', 
            './advanced_output',
            theme='dark'
        )
        print(f"- HTML output: {html_output}")
        
        # Quiz with extended settings
        quiz_output = generator.generate(
            processed_content,
            'quiz',
            './advanced_output'
        )
        print(f"- Quiz output: {quiz_output}")
        
        print("\n✅ Advanced configuration demonstration completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # Clean up
        for file_path in [config_file, sample_file]:
            if file_path.exists():
                file_path.unlink()


def environment_variable_config():
    """Demonstrate environment variable configuration"""
    print("\n=== Environment Variable Configuration ===")
    
    import os
    
    # Set environment variables
    env_vars = {
        'GOOGLE_TRANSLATE_API_KEY': 'demo_api_key_12345',
        'DOCUMORPH_LOG_LEVEL': 'INFO',
        'DOCUMORPH_MAX_FILE_SIZE': '75MB',
        'DOCUMORPH_OUTPUT_DIR': './env_output'
    }
    
    print("Setting environment variables:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key}={value}")
    
    # Reload configuration to pick up environment variables
    config_manager._load_configuration()
    
    print("\nConfiguration values from environment:")
    print(f"- Log level: {config_manager.get('logging.level')}")
    print(f"- Max file size: {config_manager.get('processing.max_file_size')}")
    
    # Clean up environment variables
    for key in env_vars:
        if key in os.environ:
            del os.environ[key]


def profile_based_config():
    """Demonstrate profile-based configuration"""
    print("\n=== Profile-Based Configuration ===")
    
    profiles = {
        'development': {
            'logging': {'level': 'DEBUG'},
            'processing': {'max_file_size': '10MB'},
            'features': {'experimental_parsers': True}
        },
        'production': {
            'logging': {'level': 'WARNING'},
            'processing': {'max_file_size': '100MB'},
            'features': {'experimental_parsers': False}
        },
        'testing': {
            'logging': {'level': 'ERROR'},
            'processing': {'max_file_size': '5MB'},
            'features': {'experimental_parsers': True}
        }
    }
    
    for profile_name, profile_config in profiles.items():
        print(f"\n{profile_name.upper()} Profile:")
        
        # Create profile config file
        profile_file = Path(f'{profile_name}_config.yaml')
        with open(profile_file, 'w', encoding='utf-8') as f:
            yaml.dump(profile_config, f, default_flow_style=False, indent=2)
        
        # Load and show configuration
        config_manager.load_user_config(str(profile_file))
        
        print(f"  - Log level: {config_manager.get('logging.level')}")
        print(f"  - Max file size: {config_manager.get('processing.max_file_size')}")
        print(f"  - Experimental features: {config_manager.get('features.experimental_parsers')}")
        
        # Clean up
        profile_file.unlink()


def configuration_validation():
    """Demonstrate configuration validation"""
    print("\n=== Configuration Validation ===")
    
    # Test invalid configurations
    invalid_configs = [
        {
            'name': 'Invalid file size',
            'config': {'processing': {'max_file_size': 'invalid_size'}},
            'expected_error': 'Invalid file size format'
        },
        {
            'name': 'Missing required section',
            'config': {'invalid_section': {'some_value': True}},
            'expected_error': 'Missing required configuration section'
        }
    ]
    
    for test_case in invalid_configs:
        print(f"\nTesting: {test_case['name']}")
        
        # Create invalid config file
        invalid_file = Path('invalid_config.yaml')
        with open(invalid_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_case['config'], f)
        
        try:
            config_manager.load_user_config(str(invalid_file))
            print("  ⚠️  Configuration loaded without validation error")
        except Exception as e:
            print(f"  ✅ Validation caught error: {e}")
        
        # Clean up
        invalid_file.unlink()


if __name__ == '__main__':
    print("DocuMorph Advanced Configuration Examples")
    print("=" * 60)
    
    try:
        demonstrate_config_usage()
        environment_variable_config()
        profile_based_config()
        configuration_validation()
        
        print("\n" + "=" * 60)
        print("Advanced configuration examples completed!")
        print("\nKey takeaways:")
        print("- Use YAML files for complex configurations")
        print("- Environment variables for sensitive data")
        print("- Profile-based configs for different environments")
        print("- Always validate configuration inputs")
        
    except Exception as e:
        print(f"\n❌ Error in advanced configuration examples: {e}")
        import traceback
        traceback.print_exc()
