#!/usr/bin/env python3
"""
Content Processor for DocuMorph

This module processes parsed document content and prepares it for output generation.
"""

import logging
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .document_parser import ParsedDocument
from ..utils.validators import ContentValidator
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


@dataclass
class ProcessedContent:
    """Container for processed content data"""
    content: str
    metadata: Dict[str, Any]
    structure: Dict[str, Any]
    sections: List[Dict[str, Any]]
    summary: str
    keywords: List[str]
    reading_time: int
    difficulty_level: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            'content': self.content,
            'metadata': self.metadata,
            'structure': self.structure,
            'sections': self.sections,
            'summary': self.summary,
            'keywords': self.keywords,
            'reading_time': self.reading_time,
            'difficulty_level': self.difficulty_level
        }


class ContentProcessor:
    """
    Processes document content for output generation
    """
    
    def __init__(self):
        """Initialize the content processor"""
        self.validator = ContentValidator()
        self.min_section_length = config_manager.get('processing.min_section_length', 100)
        self.max_summary_length = config_manager.get('processing.max_summary_length', 500)
        
        logger.info("ContentProcessor initialized")
    
    def process(self, parsed_doc: ParsedDocument, **kwargs) -> ProcessedContent:
        """
        Process parsed document content
        
        Args:
            parsed_doc: ParsedDocument object
            **kwargs: Additional processing options
                - enhance_structure: bool - Enhance document structure (default: True)
                - generate_summary: bool - Generate content summary (default: True)
                - extract_keywords: bool - Extract keywords (default: True)
                - calculate_metrics: bool - Calculate reading metrics (default: True)
        
        Returns:
            ProcessedContent object
        """
        try:
            enhance_structure = kwargs.get('enhance_structure', True)
            generate_summary = kwargs.get('generate_summary', True)
            extract_keywords = kwargs.get('extract_keywords', True)
            calculate_metrics = kwargs.get('calculate_metrics', True)
            
            logger.info(f"Processing content from {parsed_doc.format} document")
            
            # Validate and clean content
            content = self._clean_content(parsed_doc.content)
            
            # Enhance structure if requested
            structure = parsed_doc.structure.copy()
            if enhance_structure:
                structure = self._enhance_structure(content, structure)
            
            # Extract sections
            sections = self._extract_sections(content, structure)
            
            # Generate summary
            summary = ""
            if generate_summary:
                summary = self._generate_summary(content, sections)
            
            # Extract keywords
            keywords = []
            if extract_keywords:
                keywords = self._extract_keywords(content)
            
            # Calculate metrics
            reading_time = 0
            difficulty_level = "medium"
            if calculate_metrics:
                reading_time = self._calculate_reading_time(content)
                difficulty_level = self._assess_difficulty(content)
            
            # Create processed content
            processed = ProcessedContent(
                content=content,
                metadata=parsed_doc.metadata,
                structure=structure,
                sections=sections,
                summary=summary,
                keywords=keywords,
                reading_time=reading_time,
                difficulty_level=difficulty_level
            )
            
            logger.info(f"Content processing completed: {len(sections)} sections, "
                       f"{reading_time}min read, {difficulty_level} difficulty")
            
            return processed
            
        except Exception as e:
            logger.error(f"Error processing content: {e}")
            raise
    
    def _clean_content(self, content: str) -> str:
        """
        Clean and normalize content
        
        Args:
            content: Raw content
            
        Returns:
            Cleaned content
        """
        # Use validator to sanitize
        content = self.validator.sanitize_content(content)
        
        # Additional cleaning
        # Fix common OCR/parsing errors
        content = re.sub(r'([a-z])([A-Z])', r'\1 \2', content)  # Add spaces between words
        content = re.sub(r'(\w)-\s*\n\s*(\w)', r'\1\2', content)  # Fix hyphenated words
        content = re.sub(r'\s+', ' ', content)  # Normalize whitespace
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)  # Normalize line breaks
        
        # Remove page numbers and headers/footers (simple heuristics)
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            
            # Skip likely page numbers
            if re.match(r'^\d+$', line) and len(line) <= 3:
                continue
            
            # Skip likely headers/footers (short lines with common patterns)
            if (len(line) < 50 and 
                any(pattern in line.lower() for pattern in ['page', 'chapter', 'section'])):
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines).strip()
    
    def _enhance_structure(self, content: str, structure: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance document structure analysis
        
        Args:
            content: Document content
            structure: Existing structure
            
        Returns:
            Enhanced structure
        """
        enhanced = structure.copy()
        
        # Improve heading detection
        enhanced['headings'] = self._improve_heading_detection(content, structure.get('headings', []))
        
        # Detect document type
        enhanced['document_type'] = self._detect_document_type(content)
        
        # Analyze content flow
        enhanced['content_flow'] = self._analyze_content_flow(content)
        
        # Extract key phrases
        enhanced['key_phrases'] = self._extract_key_phrases(content)
        
        return enhanced
    
    def _improve_heading_detection(self, content: str, existing_headings: List[Dict]) -> List[Dict]:
        """
        Improve heading detection using multiple heuristics
        
        Args:
            content: Document content
            existing_headings: Existing headings from parser
            
        Returns:
            Improved headings list
        """
        headings = existing_headings.copy()
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line or len(line) > 100:
                continue
            
            # Check if already detected
            if any(h.get('text', '').strip() == line for h in headings):
                continue
            
            # Additional heading patterns
            is_heading = False
            level = 3
            
            # All caps and reasonable length
            if line.isupper() and 10 <= len(line) <= 80:
                is_heading = True
                level = 1
            
            # Title case with specific patterns
            elif (line.istitle() and 
                  not line.endswith('.') and
                  len(line.split()) <= 8 and
                  not any(word.lower() in ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'] 
                         for word in line.split()[1:])):
                is_heading = True
                level = 2
            
            # Numbered sections
            elif re.match(r'^\d+(\.\d+)*\.?\s+[A-Z]', line):
                is_heading = True
                level = line.count('.') + 1
            
            if is_heading:
                headings.append({
                    'text': line,
                    'level': level,
                    'line_number': i + 1,
                    'enhanced': True
                })
        
        # Sort by line number
        headings.sort(key=lambda x: x.get('line_number', 0))
        
        return headings
    
    def _detect_document_type(self, content: str) -> str:
        """
        Detect document type from content
        
        Args:
            content: Document content
            
        Returns:
            Document type string
        """
        content_lower = content.lower()
        
        # Academic paper indicators
        if any(term in content_lower for term in ['abstract', 'introduction', 'methodology', 'conclusion', 'references']):
            return 'academic'
        
        # Technical documentation
        elif any(term in content_lower for term in ['api', 'function', 'parameter', 'example', 'usage']):
            return 'technical'
        
        # Business document
        elif any(term in content_lower for term in ['executive summary', 'objectives', 'strategy', 'budget', 'timeline']):
            return 'business'
        
        # Manual/guide
        elif any(term in content_lower for term in ['step', 'procedure', 'instructions', 'how to', 'guide']):
            return 'manual'
        
        # Report
        elif any(term in content_lower for term in ['findings', 'analysis', 'recommendations', 'data', 'results']):
            return 'report'
        
        else:
            return 'general'
    
    def _analyze_content_flow(self, content: str) -> Dict[str, Any]:
        """
        Analyze content flow and structure
        
        Args:
            content: Document content
            
        Returns:
            Content flow analysis
        """
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        return {
            'paragraph_count': len(paragraphs),
            'avg_paragraph_length': sum(len(p) for p in paragraphs) / len(paragraphs) if paragraphs else 0,
            'short_paragraphs': len([p for p in paragraphs if len(p) < 100]),
            'long_paragraphs': len([p for p in paragraphs if len(p) > 500]),
            'has_introduction': self._has_introduction(paragraphs),
            'has_conclusion': self._has_conclusion(paragraphs)
        }
    
    def _has_introduction(self, paragraphs: List[str]) -> bool:
        """Check if document has an introduction"""
        if not paragraphs:
            return False
        
        first_para = paragraphs[0].lower()
        intro_indicators = ['introduction', 'overview', 'background', 'this document', 'this paper']
        
        return any(indicator in first_para for indicator in intro_indicators)
    
    def _has_conclusion(self, paragraphs: List[str]) -> bool:
        """Check if document has a conclusion"""
        if not paragraphs:
            return False
        
        last_para = paragraphs[-1].lower()
        conclusion_indicators = ['conclusion', 'summary', 'in summary', 'to conclude', 'finally']
        
        return any(indicator in last_para for indicator in conclusion_indicators)
    
    def _extract_key_phrases(self, content: str) -> List[str]:
        """
        Extract key phrases from content
        
        Args:
            content: Document content
            
        Returns:
            List of key phrases
        """
        # Simple key phrase extraction using frequency and patterns
        words = re.findall(r'\b[A-Za-z]{3,}\b', content.lower())
        
        # Count word frequency
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Filter common words
        common_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be', 'been',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'can', 'may', 'might', 'must', 'shall', 'not', 'no', 'yes', 'all', 'any',
            'some', 'many', 'much', 'more', 'most', 'other', 'such', 'very', 'just',
            'only', 'also', 'even', 'well', 'way', 'use', 'used', 'using', 'make',
            'made', 'making', 'get', 'got', 'getting', 'take', 'taken', 'taking'
        }
        
        # Get significant words
        significant_words = {
            word: freq for word, freq in word_freq.items() 
            if word not in common_words and freq > 2 and len(word) > 3
        }
        
        # Sort by frequency and return top phrases
        key_phrases = sorted(significant_words.keys(), 
                           key=lambda x: significant_words[x], 
                           reverse=True)[:20]
        
        return key_phrases
    
    def _extract_sections(self, content: str, structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract document sections
        
        Args:
            content: Document content
            structure: Document structure
            
        Returns:
            List of section dictionaries
        """
        sections = []
        headings = structure.get('headings', [])
        
        if not headings:
            # No headings found, treat entire content as one section
            sections.append({
                'title': 'Main Content',
                'content': content,
                'level': 1,
                'word_count': len(content.split()),
                'start_line': 1
            })
            return sections
        
        lines = content.split('\n')
        
        for i, heading in enumerate(headings):
            start_line = heading.get('line_number', 1) - 1
            
            # Find end line (next heading or end of document)
            if i + 1 < len(headings):
                end_line = headings[i + 1].get('line_number', len(lines)) - 1
            else:
                end_line = len(lines)
            
            # Extract section content
            section_lines = lines[start_line:end_line]
            section_content = '\n'.join(section_lines).strip()
            
            # Skip if section is too short
            if len(section_content) < self.min_section_length:
                continue
            
            sections.append({
                'title': heading.get('text', ''),
                'content': section_content,
                'level': heading.get('level', 1),
                'word_count': len(section_content.split()),
                'start_line': start_line + 1,
                'end_line': end_line
            })
        
        return sections
    
    def _generate_summary(self, content: str, sections: List[Dict[str, Any]]) -> str:
        """
        Generate content summary
        
        Args:
            content: Document content
            sections: Document sections
            
        Returns:
            Content summary
        """
        # Simple extractive summarization
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip() and len(p) > 50]
        
        if not paragraphs:
            return "No summary available."
        
        # Score paragraphs based on position and content
        scored_paragraphs = []
        
        for i, paragraph in enumerate(paragraphs):
            score = 0
            
            # Position scoring (first and last paragraphs are important)
            if i == 0:
                score += 3
            elif i == len(paragraphs) - 1:
                score += 2
            elif i < len(paragraphs) * 0.2:  # First 20%
                score += 1
            
            # Content scoring (look for summary indicators)
            summary_words = ['summary', 'conclusion', 'important', 'key', 'main', 'primary']
            for word in summary_words:
                if word in paragraph.lower():
                    score += 1
            
            # Length scoring (prefer medium-length paragraphs)
            if 100 <= len(paragraph) <= 300:
                score += 1
            
            scored_paragraphs.append((paragraph, score))
        
        # Sort by score and select top paragraphs
        scored_paragraphs.sort(key=lambda x: x[1], reverse=True)
        
        summary_parts = []
        total_length = 0
        
        for paragraph, score in scored_paragraphs:
            if total_length + len(paragraph) <= self.max_summary_length:
                summary_parts.append(paragraph)
                total_length += len(paragraph)
            else:
                break
        
        return ' '.join(summary_parts) if summary_parts else paragraphs[0][:self.max_summary_length]
    
    def _extract_keywords(self, content: str) -> List[str]:
        """
        Extract keywords from content
        
        Args:
            content: Document content
            
        Returns:
            List of keywords
        """
        # Use the key phrases extraction but filter for single words
        key_phrases = self._extract_key_phrases(content)
        
        # Filter to single words and add some multi-word phrases
        keywords = []
        
        for phrase in key_phrases:
            if ' ' not in phrase:  # Single word
                keywords.append(phrase)
        
        # Add some important multi-word phrases
        content_lower = content.lower()
        important_phrases = [
            'machine learning', 'artificial intelligence', 'data science',
            'software development', 'project management', 'business analysis',
            'quality assurance', 'user experience', 'customer service'
        ]
        
        for phrase in important_phrases:
            if phrase in content_lower and phrase not in keywords:
                keywords.append(phrase)
        
        return keywords[:15]  # Limit to top 15 keywords
    
    def _calculate_reading_time(self, content: str) -> int:
        """
        Calculate estimated reading time in minutes
        
        Args:
            content: Document content
            
        Returns:
            Reading time in minutes
        """
        words = len(content.split())
        # Average reading speed: 200-250 words per minute
        reading_speed = 225
        
        return max(1, round(words / reading_speed))
    
    def _assess_difficulty(self, content: str) -> str:
        """
        Assess content difficulty level
        
        Args:
            content: Document content
            
        Returns:
            Difficulty level string
        """
        words = content.split()
        sentences = re.split(r'[.!?]+', content)
        
        if not words or not sentences:
            return 'medium'
        
        # Calculate metrics
        avg_word_length = sum(len(word) for word in words) / len(words)
        avg_sentence_length = len(words) / len(sentences)
        
        # Count complex words (3+ syllables, simplified)
        complex_words = len([word for word in words if len(word) > 6])
        complex_ratio = complex_words / len(words)
        
        # Simple difficulty assessment
        difficulty_score = 0
        
        if avg_word_length > 5:
            difficulty_score += 1
        if avg_sentence_length > 20:
            difficulty_score += 1
        if complex_ratio > 0.15:
            difficulty_score += 1
        
        # Check for technical terms
        technical_indicators = ['algorithm', 'implementation', 'methodology', 'analysis', 'framework']
        if any(term in content.lower() for term in technical_indicators):
            difficulty_score += 1
        
        if difficulty_score <= 1:
            return 'easy'
        elif difficulty_score <= 2:
            return 'medium'
        else:
            return 'hard'
