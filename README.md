# DocuMorph 📄➡️🌐

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![GitHub issues](https://img.shields.io/github/issues/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas)](https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas/issues)
[![Version](https://img.shields.io/badge/version-1.0.0-orange.svg)](https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas/releases)

A comprehensive document transformation CLI tool that converts documents (PDF, DOCX, TXT, MD) into multiple output formats including responsive web pages, interactive quizzes, audio podcasts, and multi-language content.

## ✨ Features

### 📖 Document Parsing
- **PDF Support**: Extract text, metadata, and structure from PDF files using PyPDF2
- **DOCX Support**: Parse Microsoft Word documents with formatting preservation
- **Plain Text**: Handle text files with automatic encoding detection
- **Markdown**: Full Markdown parsing with front matter and structure analysis

### 🎯 Output Formats
- **📱 Responsive HTML/CSS**: Modern, mobile-friendly web pages with customizable themes
- **🧠 Interactive Quizzes**: Auto-generated quizzes with multiple question types
- **🎵 Audio Podcasts**: Text-to-speech conversion using Google TTS
- **🌍 Multi-language Translation**: Support for 19+ languages using Google Translate

### 🚀 Key Capabilities
- **CLI-First Design**: Powerful command-line interface for automation
- **Batch Processing**: Handle multiple documents simultaneously with progress tracking
- **Free APIs Only**: Uses Google Translate and gTTS (no paid APIs required)
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Modular Architecture**: Clean, extensible codebase following PEP 8 standards

## 🎬 Quick Demo

```bash
# Convert PDF to responsive HTML
documorph convert document.pdf --output-format html

# Generate interactive quiz from DOCX
documorph convert presentation.docx --output-format quiz

# Create audio podcast from Markdown
documorph convert readme.md --output-format audio

# Translate document to multiple languages
documorph convert manual.txt --output-format translation --languages es,fr,de

# Process multiple files at once
documorph batch-convert ./documents --output-format html,quiz
```

## 📦 Installation

### Quick Install
```bash
pip install documorph
```

### From Source
```bash
git clone https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas.git
cd DocuMorph_AugmentCode_GeminiCanvas
pip install -e .
```

### With All Dependencies
```bash
pip install documorph[all]
```

### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Memory**: 512 MB RAM (2 GB recommended)
- **Storage**: 100 MB free space
- **Internet**: Required for translation and TTS services

## 🏗️ System Architecture

```mermaid
graph TB
    A[CLI Interface] --> B[Input Parser]
    B --> C{Document Type}
    C -->|PDF| D[PDF Parser]
    C -->|DOCX| E[DOCX Parser]
    C -->|TXT| F[TXT Parser]
    C -->|MD| G[Markdown Parser]
    
    D --> H[Content Processor]
    E --> H
    F --> H
    G --> H
    
    H --> I{Output Format}
    I -->|HTML/CSS| J[Web Generator]
    I -->|Quiz| K[Quiz Generator]
    I -->|Audio| L[TTS Processor]
    I -->|Translation| M[Translation Engine]
    
    J --> N[Static Web Output]
    K --> O[Interactive Quiz]
    L --> P[Audio Files]
    M --> Q[Translated Content]
    
    R[Configuration Manager] --> H
    S[API Manager] --> L
    S --> M
    T[Error Handler] --> A
```

## 🔄 Workflow Diagram

```mermaid
flowchart LR
    A[Input Document] --> B[Document Analysis]
    B --> C[Content Extraction]
    C --> D[Content Processing]
    D --> E{Transformation Type}
    
    E -->|Web| F[HTML/CSS Generation]
    E -->|Quiz| G[Question Generation]
    E -->|Audio| H[TTS Conversion]
    E -->|Translation| I[Language Processing]
    
    F --> J[Responsive Web Page]
    G --> K[Interactive Quiz JSON/HTML]
    H --> L[Audio Podcast MP3/WAV]
    I --> M[Multi-language Content]
    
    N[Configuration] --> D
    O[Free APIs] --> H
    O --> I
    P[Error Logging] --> Q[User Feedback]
```

## 📁 Project Directory Structure

```
DocuMorph_AugmentCode_GeminiCanvas/
├── README.md                          # This file - comprehensive documentation
├── LICENSE                            # MIT License
├── requirements.txt                   # Python dependencies
├── setup.py                          # Package installation script
├── .gitignore                        # Git ignore patterns
├── config/
│   ├── __init__.py
│   ├── default_config.yaml           # Default configuration settings
│   └── config_manager.py             # Configuration management
├── documorph/
│   ├── __init__.py                   # Package initialization
│   ├── cli.py                        # Main CLI interface
│   ├── core/
│   │   ├── __init__.py
│   │   ├── document_parser.py        # Document parsing logic
│   │   ├── content_processor.py      # Content processing algorithms
│   │   └── output_generator.py       # Output format generation
│   ├── parsers/
│   │   ├── __init__.py
│   │   ├── pdf_parser.py             # PDF document parsing
│   │   ├── docx_parser.py            # DOCX document parsing
│   │   ├── txt_parser.py             # Plain text parsing
│   │   └── markdown_parser.py        # Markdown parsing
│   ├── generators/
│   │   ├── __init__.py
│   │   ├── web_generator.py          # HTML/CSS generation
│   │   ├── quiz_generator.py         # Interactive quiz creation
│   │   ├── audio_generator.py        # TTS audio generation
│   │   └── translation_generator.py  # Multi-language translation
│   ├── apis/
│   │   ├── __init__.py
│   │   ├── google_translate.py       # Google Translate API integration
│   │   └── tts_services.py           # Free TTS services integration
│   └── utils/
│       ├── __init__.py
│       ├── file_handler.py           # File operations
│       ├── logger.py                 # Logging configuration
│       └── validators.py             # Input validation
├── templates/
│   ├── web/
│   │   ├── base.html                 # Base HTML template
│   │   ├── styles.css                # Responsive CSS styles
│   │   └── script.js                 # Interactive JavaScript
│   └── quiz/
│       ├── quiz_template.html        # Quiz HTML template
│       └── quiz_styles.css           # Quiz-specific styles
├── tests/
│   ├── __init__.py
│   ├── test_parsers.py               # Parser unit tests
│   ├── test_generators.py            # Generator unit tests
│   ├── test_cli.py                   # CLI interface tests
│   └── fixtures/                     # Test documents and data
│       ├── sample.pdf
│       ├── sample.docx
│       ├── sample.txt
│       └── sample.md
├── examples/
│   ├── basic_usage.py                # Basic usage examples
│   ├── advanced_config.py            # Advanced configuration examples
│   └── sample_documents/             # Sample input documents
└── docs/
    ├── installation.md               # Installation guide
    ├── usage.md                      # Usage documentation
    ├── api_integration.md            # API integration guide
    └── contributing.md               # Contribution guidelines
```

## 🚀 Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager
- Internet connection (for API services)

### Install from Source
```bash
git clone https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas.git
cd DocuMorph_AugmentCode_GeminiCanvas
pip install -r requirements.txt
pip install -e .
```

### Install from PyPI (Future)
```bash
pip install documorph
```

## 📖 Usage

### Basic Usage
```bash
# Convert PDF to responsive HTML
documorph convert input.pdf --output-format html --output-dir ./output

# Generate interactive quiz from DOCX
documorph convert document.docx --output-format quiz --output-dir ./quiz_output

# Create audio podcast from Markdown
documorph convert readme.md --output-format audio --voice en-us --output-dir ./audio

# Translate document to multiple languages
documorph convert document.txt --output-format translation --languages es,fr,de --output-dir ./translations
```

### Advanced Usage
```bash
# Custom configuration
documorph convert input.pdf --config custom_config.yaml --output-format html

# Multiple output formats
documorph convert document.docx --output-format html,quiz,audio --output-dir ./multi_output

# Batch processing
documorph batch-convert ./input_folder --output-format html --output-dir ./batch_output
```

## 📊 Supported Formats

### Input Formats
| Format | Extension | Description | Features |
|--------|-----------|-------------|----------|
| **PDF** | `.pdf` | Portable Document Format | Text extraction, metadata, structure |
| **DOCX** | `.docx` | Microsoft Word Document | Formatting, tables, images, metadata |
| **TXT** | `.txt` | Plain Text Files | Encoding detection, structure analysis |
| **Markdown** | `.md` | Markdown Documents | Front matter, syntax highlighting |

### Output Formats
| Format | Description | Features |
|--------|-------------|----------|
| **HTML/CSS** | Responsive web pages | Themes, TOC, mobile-friendly, dark mode |
| **Quiz** | Interactive quizzes | Multiple choice, true/false, short answer |
| **Audio** | Audio podcasts | Natural voices, multiple languages, MP3/WAV |
| **Translation** | Multi-language content | 19+ languages, formatting preservation |

### Supported Languages (Translation & TTS)
🇪🇸 Spanish • 🇫🇷 French • 🇩🇪 German • 🇮🇹 Italian • 🇵🇹 Portuguese • 🇷🇺 Russian • 🇯🇵 Japanese • 🇰🇷 Korean • 🇨🇳 Chinese • 🇸🇦 Arabic • 🇮🇳 Hindi • 🇹🇷 Turkish • 🇵🇱 Polish • 🇳🇱 Dutch • 🇸🇪 Swedish • 🇩🇰 Danish • 🇳🇴 Norwegian • 🇫🇮 Finnish

## ⚙️ Configuration

DocuMorph uses YAML configuration files for customization:

```yaml
# config/default_config.yaml
output:
  html:
    responsive: true
    theme: "modern"
    include_toc: true
  quiz:
    question_types: ["multiple_choice", "true_false", "short_answer"]
    difficulty_levels: ["easy", "medium", "hard"]
  audio:
    voice: "en-us"
    speed: 1.0
    format: "mp3"
  translation:
    preserve_formatting: true
    target_languages: ["es", "fr", "de"]

apis:
  google_translate:
    api_key: "your_api_key_here"
    rate_limit: 100
  tts_service:
    provider: "gtts"  # Google Text-to-Speech (free)
    rate_limit: 50

processing:
  max_file_size: "50MB"
  supported_formats: ["pdf", "docx", "txt", "md"]
  output_quality: "high"
```

## 🔌 API Integration

### Google Translate API (Free Tier)
```python
# Set up Google Translate API
export GOOGLE_TRANSLATE_API_KEY="your_api_key"
```

### Free TTS Services
- **Google Text-to-Speech (gTTS)** - Free, no API key required
- **eSpeak** - Open source, offline TTS
- **Festival** - University of Edinburgh TTS system

## 🧪 Testing

Run the test suite:
```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/test_parsers.py
python -m pytest tests/test_generators.py

# Run with coverage
python -m pytest tests/ --cov=documorph --cov-report=html
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](docs/contributing.md) for guidelines.

### Development Setup
```bash
git clone https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas.git
cd DocuMorph_AugmentCode_GeminiCanvas
pip install -r requirements.txt
pip install -e .[dev]
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**HectorTa1989**
- GitHub: [@HectorTa1989](https://github.com/HectorTa1989)
- Project: [DocuMorph](https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas)

## 🙏 Acknowledgments

- Built with Python and open-source libraries
- Powered by free APIs and services
- Designed for cross-platform compatibility

---

**DocuMorph** - Transform your documents, amplify your content! 🚀
