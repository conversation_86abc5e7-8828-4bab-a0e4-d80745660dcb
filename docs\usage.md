# DocuMorph Usage Guide

This comprehensive guide covers all aspects of using DocuMorph for document transformation.

## Quick Start

### Basic Conversion
```bash
# Convert PDF to HTML
documorph convert document.pdf --output-format html

# Convert DOCX to quiz
documorph convert presentation.docx --output-format quiz

# Convert Markdown to audio
documorph convert readme.md --output-format audio
```

### Multiple Output Formats
```bash
# Generate multiple formats at once
documorph convert document.pdf --output-format html,quiz,audio
```

## Command Reference

### Global Options

```bash
documorph [GLOBAL_OPTIONS] COMMAND [COMMAND_OPTIONS]
```

**Global Options:**
- `--version`: Show version information
- `--config PATH`: Use custom configuration file
- `--verbose, -v`: Enable verbose output
- `--quiet, -q`: Suppress output
- `--help`: Show help message

### Commands Overview

| Command | Description |
|---------|-------------|
| `convert` | Convert a single document |
| `batch-convert` | Convert multiple documents |
| `info` | Show document information |
| `formats` | List supported formats |
| `config` | Show configuration |

## Convert Command

The `convert` command is the primary tool for document transformation.

### Syntax
```bash
documorph convert INPUT_FILE [OPTIONS]
```

### Options

#### Output Format (`--output-format`, `-f`)
Specify one or more output formats:

```bash
# Single format
documorph convert doc.pdf -f html

# Multiple formats
documorph convert doc.pdf -f html -f quiz -f audio

# Comma-separated
documorph convert doc.pdf -f html,quiz,audio
```

**Supported formats:**
- `html`: Responsive web pages
- `quiz`: Interactive quizzes
- `audio`: Audio podcasts
- `translation`: Multi-language content

#### Output Directory (`--output-dir`, `-o`)
Specify where to save generated files:

```bash
documorph convert doc.pdf -f html -o ./my_output
```

#### Language Options (`--languages`, `-l`)
For translation output, specify target languages:

```bash
documorph convert doc.pdf -f translation -l es,fr,de,it
```

#### Voice Options (`--voice`)
For audio output, specify voice language:

```bash
documorph convert doc.pdf -f audio --voice en-us
```

#### Theme Options (`--theme`)
For HTML output, specify theme:

```bash
documorph convert doc.pdf -f html --theme modern
```

### Examples

#### Basic HTML Generation
```bash
# Simple conversion
documorph convert report.pdf --output-format html

# With custom output directory
documorph convert report.pdf -f html -o ./web_output

# With specific theme
documorph convert report.pdf -f html --theme dark
```

#### Quiz Generation
```bash
# Generate interactive quiz
documorph convert textbook.pdf -f quiz

# Quiz with custom output location
documorph convert textbook.pdf -f quiz -o ./quizzes
```

#### Audio Generation
```bash
# Generate English audio
documorph convert article.md -f audio

# Generate Spanish audio
documorph convert article.md -f audio --voice es

# Audio with custom output
documorph convert article.md -f audio -o ./podcasts
```

#### Multi-language Translation
```bash
# Translate to Spanish and French
documorph convert manual.docx -f translation -l es,fr

# Translate to multiple languages
documorph convert guide.txt -f translation -l es,fr,de,it,pt
```

#### Combined Outputs
```bash
# Generate all formats
documorph convert document.pdf -f html,quiz,audio,translation -l es,fr

# Comprehensive conversion with custom settings
documorph convert book.docx \
  --output-format html,quiz,audio \
  --output-dir ./complete_output \
  --theme modern \
  --voice en-us
```

## Batch Convert Command

Process multiple documents at once.

### Syntax
```bash
documorph batch-convert INPUT_DIRECTORY [OPTIONS]
```

### Options

#### Pattern Matching (`--pattern`)
Specify file patterns to match:

```bash
# Process all PDF files
documorph batch-convert ./docs --pattern "*.pdf"

# Process all Markdown files
documorph batch-convert ./content --pattern "*.md"
```

#### Recursive Processing (`--recursive`, `-r`)
Search subdirectories:

```bash
# Process all files recursively
documorph batch-convert ./documents -r

# Process PDFs recursively
documorph batch-convert ./archive --pattern "*.pdf" -r
```

### Examples

#### Basic Batch Processing
```bash
# Convert all files in directory to HTML
documorph batch-convert ./documents -f html

# Convert all PDFs to quizzes
documorph batch-convert ./pdfs --pattern "*.pdf" -f quiz
```

#### Advanced Batch Processing
```bash
# Recursive processing with multiple formats
documorph batch-convert ./content \
  --pattern "*.md" \
  --recursive \
  --output-format html,quiz \
  --output-dir ./batch_output
```

## Info Command

Get detailed information about a document.

### Syntax
```bash
documorph info INPUT_FILE
```

### Example
```bash
documorph info document.pdf
```

**Output includes:**
- File format and size
- Metadata (title, author, etc.)
- Structure information (pages, sections, etc.)
- Content statistics

## Formats Command

List all supported input and output formats.

### Syntax
```bash
documorph formats
```

## Config Command

Display current configuration settings.

### Syntax
```bash
documorph config [--show-all]
```

### Options
- `--show-all`: Show complete configuration

## Configuration

### Configuration File

DocuMorph uses YAML configuration files. Create a custom config:

```yaml
# my_config.yaml
output:
  html:
    responsive: true
    theme: "modern"
    include_toc: true
    dark_mode: false

  quiz:
    question_types: ["multiple_choice", "true_false", "short_answer"]
    difficulty_levels: ["easy", "medium", "hard"]
    questions_per_section: 5
    randomize_questions: true

  audio:
    voice: "en-us"
    speed: 1.0
    format: "mp3"
    add_intro: true

  translation:
    preserve_formatting: true
    target_languages: ["es", "fr", "de"]

processing:
  max_file_size: "50MB"
  parallel_processing: true
  max_workers: 4

apis:
  google_translate:
    rate_limit: 100
  tts_service:
    provider: "gtts"
    rate_limit: 50
```

Use with:
```bash
documorph --config my_config.yaml convert document.pdf -f html
```

### Environment Variables

Set API keys and configuration via environment variables:

```bash
# Google Translate API key
export GOOGLE_TRANSLATE_API_KEY="your_api_key"

# Log level
export DOCUMORPH_LOG_LEVEL="DEBUG"

# Max file size
export DOCUMORPH_MAX_FILE_SIZE="100MB"
```