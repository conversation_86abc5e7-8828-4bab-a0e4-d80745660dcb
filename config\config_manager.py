#!/usr/bin/env python3
"""
Configuration Manager for DocuMorph

This module handles loading, validating, and managing configuration settings
for the DocuMorph application.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class ConfigPaths:
    """Configuration file paths"""
    default_config: Path
    user_config: Optional[Path] = None
    env_config: Optional[Path] = None


class ConfigManager:
    """
    Manages configuration loading and validation for DocuMorph
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the configuration manager
        
        Args:
            config_dir: Optional custom configuration directory
        """
        self.config_dir = Path(config_dir) if config_dir else Path(__file__).parent
        self.config = {}
        self._load_configuration()
    
    def _load_configuration(self) -> None:
        """Load configuration from multiple sources"""
        try:
            # Load default configuration
            default_config_path = self.config_dir / "default_config.yaml"
            if default_config_path.exists():
                with open(default_config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                logger.info(f"Loaded default configuration from {default_config_path}")
            else:
                logger.warning(f"Default configuration file not found: {default_config_path}")
                self.config = self._get_fallback_config()
            
            # Load user configuration if exists
            user_config_path = self.config_dir / "user_config.yaml"
            if user_config_path.exists():
                with open(user_config_path, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
                self._merge_configs(self.config, user_config)
                logger.info(f"Loaded user configuration from {user_config_path}")
            
            # Load environment variables
            self._load_env_variables()
            
            # Validate configuration
            self._validate_config()
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self.config = self._get_fallback_config()
    
    def _merge_configs(self, base: Dict[Any, Any], override: Dict[Any, Any]) -> None:
        """
        Recursively merge configuration dictionaries
        
        Args:
            base: Base configuration dictionary
            override: Override configuration dictionary
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_configs(base[key], value)
            else:
                base[key] = value
    
    def _load_env_variables(self) -> None:
        """Load configuration from environment variables"""
        env_mappings = {
            'GOOGLE_TRANSLATE_API_KEY': ['apis', 'google_translate', 'api_key'],
            'DOCUMORPH_LOG_LEVEL': ['logging', 'level'],
            'DOCUMORPH_MAX_FILE_SIZE': ['processing', 'max_file_size'],
            'DOCUMORPH_OUTPUT_DIR': ['processing', 'output_dir'],
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self._set_nested_value(self.config, config_path, value)
                logger.info(f"Set configuration from environment: {env_var}")
    
    def _set_nested_value(self, config: Dict[Any, Any], path: list, value: Any) -> None:
        """
        Set a nested configuration value
        
        Args:
            config: Configuration dictionary
            path: List of keys representing the path
            value: Value to set
        """
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value
    
    def _validate_config(self) -> None:
        """Validate the loaded configuration"""
        required_sections = ['output', 'processing', 'logging']
        
        for section in required_sections:
            if section not in self.config:
                logger.warning(f"Missing required configuration section: {section}")
                self.config[section] = {}
        
        # Validate file size format
        if 'processing' in self.config and 'max_file_size' in self.config['processing']:
            max_size = self.config['processing']['max_file_size']
            if isinstance(max_size, str) and not self._is_valid_size_format(max_size):
                logger.warning(f"Invalid file size format: {max_size}, using default")
                self.config['processing']['max_file_size'] = "50MB"
    
    def _is_valid_size_format(self, size_str: str) -> bool:
        """
        Validate file size format (e.g., "50MB", "1GB")
        
        Args:
            size_str: Size string to validate
            
        Returns:
            True if valid format, False otherwise
        """
        import re
        pattern = r'^\d+(\.\d+)?(KB|MB|GB)$'
        return bool(re.match(pattern, size_str.upper()))
    
    def _get_fallback_config(self) -> Dict[str, Any]:
        """
        Get fallback configuration when loading fails
        
        Returns:
            Minimal fallback configuration
        """
        return {
            'output': {
                'html': {'responsive': True, 'theme': 'modern'},
                'quiz': {'question_types': ['multiple_choice']},
                'audio': {'voice': 'en-us', 'format': 'mp3'},
                'translation': {'target_languages': ['es', 'fr']}
            },
            'processing': {
                'max_file_size': '50MB',
                'supported_formats': ['pdf', 'docx', 'txt', 'md']
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/documorph.log'
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key
        
        Args:
            key: Configuration key (supports dot notation, e.g., 'output.html.theme')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        current = self.config
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value by key
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
        """
        keys = key.split('.')
        self._set_nested_value(self.config, keys, value)
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get all configuration
        
        Returns:
            Complete configuration dictionary
        """
        return self.config.copy()
    
    def save_user_config(self, config_updates: Dict[str, Any]) -> None:
        """
        Save user configuration updates
        
        Args:
            config_updates: Configuration updates to save
        """
        user_config_path = self.config_dir / "user_config.yaml"
        
        try:
            # Load existing user config if it exists
            existing_config = {}
            if user_config_path.exists():
                with open(user_config_path, 'r', encoding='utf-8') as f:
                    existing_config = yaml.safe_load(f) or {}
            
            # Merge with updates
            self._merge_configs(existing_config, config_updates)
            
            # Save updated config
            with open(user_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(existing_config, f, default_flow_style=False, indent=2)
            
            logger.info(f"Saved user configuration to {user_config_path}")
            
            # Reload configuration
            self._load_configuration()
            
        except Exception as e:
            logger.error(f"Error saving user configuration: {e}")
            raise


# Global configuration instance
config_manager = ConfigManager()
