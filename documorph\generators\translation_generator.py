#!/usr/bin/env python3
"""
Translation Generator for DocuMorph

This module generates multi-language translations using free translation APIs.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List

try:
    from googletrans import Translator
    import requests
except ImportError:
    Translator = requests = None

from ..core.content_processor import ProcessedContent
from ..utils.file_handler import FileHandler
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class TranslationGenerator:
    """
    Generator for multi-language translations
    """
    
    description = "Generates multi-language translations using Google Translate (free)"
    supported_options = ['languages', 'preserve_formatting', 'translate_metadata']
    
    def __init__(self):
        """Initialize the translation generator"""
        self.file_handler = FileHandler()
        
        if Translator is None:
            logger.warning("googletrans not available, translation will be limited")
        else:
            self.translator = Translator()
        
        self.supported_languages = {
            'es': 'Spanish',
            'fr': 'French', 
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'tr': 'Turkish',
            'pl': 'Polish',
            'nl': 'Dutch',
            'sv': 'Swedish',
            'da': 'Danish',
            'no': 'Norwegian',
            'fi': 'Finnish'
        }
        
        logger.info("TranslationGenerator initialized")
    
    def generate(self, processed_content: ProcessedContent, output_dir: str, **kwargs) -> str:
        """
        Generate multi-language translations
        
        Args:
            processed_content: ProcessedContent object
            output_dir: Output directory path
            **kwargs: Generation options
                - languages: List[str] - Target languages (default: ['es', 'fr', 'de'])
                - preserve_formatting: bool - Preserve text formatting (default: True)
                - translate_metadata: bool - Translate metadata (default: True)
        
        Returns:
            Path to translation directory
        """
        try:
            if Translator is None:
                raise ImportError("googletrans is required for translation. Install with: pip install googletrans==4.0.0-rc1")
            
            # Get options
            target_languages = kwargs.get('languages', 
                                        config_manager.get('output.translation.target_languages', ['es', 'fr', 'de']))
            preserve_formatting = kwargs.get('preserve_formatting',
                                           config_manager.get('output.translation.preserve_formatting', True))
            translate_metadata = kwargs.get('translate_metadata',
                                          config_manager.get('output.translation.translate_metadata', True))
            
            # Validate languages
            valid_languages = [lang for lang in target_languages if lang in self.supported_languages]
            if not valid_languages:
                raise ValueError(f"No valid target languages provided. Supported: {list(self.supported_languages.keys())}")
            
            logger.info(f"Generating translations for languages: {valid_languages}")
            
            # Create translation directory
            title = processed_content.metadata.get('title', 'document')
            safe_title = self._sanitize_filename(title)
            
            output_path = Path(output_dir)
            translation_dir = output_path / f"{safe_title}_translations"
            translation_dir.mkdir(exist_ok=True)
            
            # Generate translations for each language
            translation_results = {}
            
            for lang_code in valid_languages:
                try:
                    lang_name = self.supported_languages[lang_code]
                    logger.info(f"Translating to {lang_name} ({lang_code})")
                    
                    translated_content = self._translate_content(
                        processed_content, 
                        lang_code,
                        preserve_formatting,
                        translate_metadata
                    )
                    
                    # Save translation
                    lang_file = translation_dir / f"{safe_title}_{lang_code}.json"
                    self.file_handler.save_json(lang_file, translated_content)
                    
                    # Generate HTML version
                    html_file = translation_dir / f"{safe_title}_{lang_code}.html"
                    html_content = self._generate_translation_html(translated_content, lang_code, lang_name)
                    self.file_handler.write_file(html_file, html_content)
                    
                    translation_results[lang_code] = {
                        'language': lang_name,
                        'json_file': str(lang_file),
                        'html_file': str(html_file),
                        'status': 'success'
                    }
                    
                except Exception as e:
                    logger.error(f"Error translating to {lang_code}: {e}")
                    translation_results[lang_code] = {
                        'language': self.supported_languages.get(lang_code, lang_code),
                        'status': 'error',
                        'error': str(e)
                    }
            
            # Create summary file
            summary_file = translation_dir / "translation_summary.json"
            summary_data = {
                'original_document': processed_content.metadata.get('file_name', ''),
                'original_title': processed_content.metadata.get('title', ''),
                'target_languages': valid_languages,
                'translations': translation_results,
                'generated_by': 'DocuMorph'
            }
            self.file_handler.save_json(summary_file, summary_data)
            
            logger.info(f"Generated translations in {translation_dir}")
            return str(translation_dir)
            
        except Exception as e:
            logger.error(f"Error generating translations: {e}")
            raise
    
    def _translate_content(self, processed_content: ProcessedContent, 
                          target_lang: str, preserve_formatting: bool,
                          translate_metadata: bool) -> Dict[str, Any]:
        """Translate content to target language"""
        
        # Translate main content
        translated_content = self._translate_text(processed_content.content, target_lang)
        
        # Translate sections
        translated_sections = []
        for section in processed_content.sections:
            translated_section = {
                'title': self._translate_text(section['title'], target_lang) if section['title'] else '',
                'content': self._translate_text(section['content'], target_lang),
                'level': section['level'],
                'word_count': section['word_count']  # Keep original count
            }
            translated_sections.append(translated_section)
        
        # Translate summary and keywords
        translated_summary = self._translate_text(processed_content.summary, target_lang) if processed_content.summary else ''
        
        # Keywords are harder to translate meaningfully, so we'll keep originals
        translated_keywords = processed_content.keywords.copy()
        
        # Translate metadata if requested
        translated_metadata = processed_content.metadata.copy()
        if translate_metadata:
            if 'title' in translated_metadata and translated_metadata['title']:
                translated_metadata['title'] = self._translate_text(translated_metadata['title'], target_lang)
            if 'subject' in translated_metadata and translated_metadata['subject']:
                translated_metadata['subject'] = self._translate_text(translated_metadata['subject'], target_lang)
        
        return {
            'content': translated_content,
            'sections': translated_sections,
            'summary': translated_summary,
            'keywords': translated_keywords,
            'metadata': translated_metadata,
            'target_language': target_lang,
            'language_name': self.supported_languages[target_lang],
            'reading_time': processed_content.reading_time,
            'difficulty_level': processed_content.difficulty_level,
            'original_language': 'en'  # Assume English source
        }
    
    def _translate_text(self, text: str, target_lang: str) -> str:
        """Translate text to target language"""
        if not text or not text.strip():
            return text
        
        try:
            # Split long text into chunks to avoid API limits
            chunks = self._split_text_for_translation(text)
            translated_chunks = []
            
            for chunk in chunks:
                if len(chunk.strip()) < 3:  # Skip very short chunks
                    translated_chunks.append(chunk)
                    continue
                
                try:
                    result = self.translator.translate(chunk, dest=target_lang, src='en')
                    translated_chunks.append(result.text)
                except Exception as e:
                    logger.warning(f"Error translating chunk: {e}")
                    translated_chunks.append(chunk)  # Keep original on error
            
            return ''.join(translated_chunks)
            
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return text  # Return original text on error
    
    def _split_text_for_translation(self, text: str, max_chars: int = 4000) -> List[str]:
        """Split text into chunks suitable for translation API"""
        if len(text) <= max_chars:
            return [text]
        
        chunks = []
        sentences = text.split('. ')
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 2 <= max_chars:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _generate_translation_html(self, translated_content: Dict[str, Any], 
                                 lang_code: str, lang_name: str) -> str:
        """Generate HTML for translated content"""
        
        title = translated_content['metadata'].get('title', 'Translated Document')
        content = translated_content['content']
        summary = translated_content['summary']
        sections = translated_content['sections']
        
        # Build sections HTML
        sections_html = ""
        for section in sections:
            if section['title']:
                sections_html += f"<h2>{section['title']}</h2>"
            sections_html += f"<div class='section-content'>{self._format_text_for_html(section['content'])}</div>"
        
        html_content = f"""<!DOCTYPE html>
<html lang="{lang_code}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f9f9f9;
        }}
        .header {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .title {{
            color: #2563eb;
            margin-bottom: 10px;
        }}
        .language-info {{
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }}
        .summary {{
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }}
        .content {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .section-content {{
            margin-bottom: 20px;
        }}
        h2 {{
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-top: 30px;
            margin-bottom: 15px;
        }}
        p {{
            margin-bottom: 15px;
            text-align: justify;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            color: #666;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">{title}</h1>
        <div class="language-info">
            Translated to {lang_name} ({lang_code.upper()}) by DocuMorph
        </div>
    </div>
    
    {f'<div class="summary"><h2>Summary</h2><p>{summary}</p></div>' if summary else ''}
    
    <div class="content">
        {sections_html if sections_html else self._format_text_for_html(content)}
    </div>
    
    <div class="footer">
        <p>This translation was generated automatically by DocuMorph using Google Translate.</p>
        <p>Please note that automatic translations may not be perfect and should be reviewed by a native speaker.</p>
    </div>
</body>
</html>"""
        
        return html_content
    
    def _format_text_for_html(self, text: str) -> str:
        """Format text for HTML display"""
        import html
        
        # Escape HTML
        text = html.escape(text)
        
        # Convert line breaks
        text = text.replace('\n\n', '</p><p>')
        text = text.replace('\n', '<br>')
        
        # Wrap in paragraphs
        if text:
            text = f'<p>{text}</p>'
        
        return text
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system usage"""
        import re
        filename = filename.replace(' ', '_')
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        return filename.lower()[:50] if filename else 'translation'
