# DocuMorph Default Configuration
# This file contains the default settings for DocuMorph

# Output format configurations
output:
  html:
    responsive: true
    theme: "modern"
    include_toc: true
    include_search: true
    dark_mode: false
    font_family: "Inter, system-ui, sans-serif"
    max_width: "1200px"
    
  quiz:
    question_types: 
      - "multiple_choice"
      - "true_false"
      - "short_answer"
      - "fill_in_blank"
    difficulty_levels: 
      - "easy"
      - "medium"
      - "hard"
    questions_per_section: 5
    randomize_questions: true
    show_explanations: true
    time_limit: 300  # seconds
    
  audio:
    voice: "en-us"
    speed: 1.0
    format: "mp3"
    quality: "high"
    add_intro: true
    add_outro: true
    chapter_breaks: true
    
  translation:
    preserve_formatting: true
    target_languages: 
      - "es"  # Spanish
      - "fr"  # French
      - "de"  # German
      - "it"  # Italian
      - "pt"  # Portuguese
    detect_source_language: true
    translate_metadata: true

# API configurations
apis:
  google_translate:
    api_key: ""  # Set via environment variable GOOGLE_TRANSLATE_API_KEY
    rate_limit: 100  # requests per minute
    timeout: 30
    retry_attempts: 3
    
  tts_service:
    provider: "gtts"  # Google Text-to-Speech (free)
    rate_limit: 50
    timeout: 60
    chunk_size: 1000  # characters per request
    
# Processing configurations
processing:
  max_file_size: "50MB"
  supported_formats: 
    - "pdf"
    - "docx"
    - "txt"
    - "md"
  output_quality: "high"
  parallel_processing: true
  max_workers: 4
  temp_dir: "./temp"
  
# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/documorph.log"
  max_size: "10MB"
  backup_count: 5
  
# Security settings
security:
  max_content_length: 52428800  # 50MB in bytes
  allowed_extensions: [".pdf", ".docx", ".txt", ".md"]
  sanitize_filenames: true
  validate_input: true
  
# Performance settings
performance:
  cache_enabled: true
  cache_dir: "./cache"
  cache_ttl: 3600  # seconds
  memory_limit: "1GB"
  
# Feature flags
features:
  experimental_parsers: false
  advanced_quiz_types: false
  batch_processing: true
  web_preview: true
