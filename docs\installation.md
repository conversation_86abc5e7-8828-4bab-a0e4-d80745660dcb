# DocuMorph Installation Guide

This guide provides detailed instructions for installing DocuMorph on different operating systems.

## System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Memory**: 512 MB RAM
- **Storage**: 100 MB free space
- **Internet**: Required for translation and TTS services

### Recommended Requirements
- **Python**: 3.9 or higher
- **Memory**: 2 GB RAM
- **Storage**: 1 GB free space for processing large documents

## Installation Methods

### Method 1: Install from PyPI (Recommended)

```bash
# Install the latest stable version
pip install documorph

# Verify installation
documorph --version
```

### Method 2: Install from Source

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas.git

# Navigate to the project directory
cd DocuMorph_AugmentCode_GeminiCanvas

# Install in development mode
pip install -e .

# Or install normally
pip install .
```

### Method 3: Install with Development Dependencies

```bash
# Clone and install with dev dependencies
git clone https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas.git
cd DocuMorph_AugmentCode_GeminiCanvas
pip install -e .[dev]
```

## Platform-Specific Instructions

### Windows

1. **Install Python 3.8+**
   - Download from [python.org](https://python.org)
   - Make sure to check "Add Python to PATH" during installation

2. **Install DocuMorph**
   ```cmd
   pip install documorph
   ```

3. **Verify Installation**
   ```cmd
   documorph --help
   ```

### macOS

1. **Install Python 3.8+**
   ```bash
   # Using Homebrew (recommended)
   brew install python

   # Or download from python.org
   ```

2. **Install DocuMorph**
   ```bash
   pip3 install documorph
   ```

3. **Verify Installation**
   ```bash
   documorph --help
   ```

### Linux (Ubuntu/Debian)

1. **Install Python 3.8+**
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip
   ```

2. **Install DocuMorph**
   ```bash
   pip3 install documorph
   ```

3. **Verify Installation**
   ```bash
   documorph --help
   ```

### Linux (CentOS/RHEL/Fedora)

1. **Install Python 3.8+**
   ```bash
   # CentOS/RHEL
   sudo yum install python3 python3-pip

   # Fedora
   sudo dnf install python3 python3-pip
   ```

2. **Install DocuMorph**
   ```bash
   pip3 install documorph
   ```

## Virtual Environment Setup (Recommended)

Using a virtual environment helps avoid dependency conflicts:

```bash
# Create virtual environment
python -m venv documorph-env

# Activate virtual environment
# On Windows:
documorph-env\Scripts\activate
# On macOS/Linux:
source documorph-env/bin/activate

# Install DocuMorph
pip install documorph

# Deactivate when done
deactivate
```

## Optional Dependencies

DocuMorph has several optional dependencies for enhanced functionality:

### For PDF Processing
```bash
pip install PyPDF2>=3.0.0
```

### For DOCX Processing
```bash
pip install python-docx>=0.8.11
```

### For Audio Generation
```bash
pip install gtts>=2.3.0 pydub>=0.25.0
```

### For Translation
```bash
pip install googletrans>=4.0.0
```

### For Web Templates
```bash
pip install jinja2>=3.1.0
```

### Install All Optional Dependencies
```bash
pip install documorph[all]
```

## Configuration

### Default Configuration
DocuMorph works out of the box with default settings. The default configuration file is located at:
- **Windows**: `%APPDATA%\DocuMorph\config.yaml`
- **macOS**: `~/Library/Application Support/DocuMorph/config.yaml`
- **Linux**: `~/.config/DocuMorph/config.yaml`

### Custom Configuration
You can create a custom configuration file:

```yaml
# config.yaml
output:
  html:
    theme: "modern"
    responsive: true
  quiz:
    question_types: ["multiple_choice", "true_false"]
  audio:
    voice: "en-us"
    format: "mp3"

processing:
  max_file_size: "50MB"
  parallel_processing: true
```

Use it with:
```bash
documorph --config config.yaml convert document.pdf
```

## API Keys Setup

### Google Translate API (Optional)
For enhanced translation features:

1. Get API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Set environment variable:
   ```bash
   export GOOGLE_TRANSLATE_API_KEY="your_api_key_here"
   ```

### Free Alternatives
DocuMorph uses free services by default:
- **Translation**: googletrans (free Google Translate)
- **TTS**: gTTS (free Google Text-to-Speech)

## Troubleshooting

### Common Issues

#### 1. "documorph: command not found"
**Solution**: Make sure Python's Scripts directory is in your PATH:
```bash
# Windows
set PATH=%PATH%;%APPDATA%\Python\Python39\Scripts

# macOS/Linux
export PATH=$PATH:~/.local/bin
```

#### 2. Permission Errors on Linux/macOS
**Solution**: Use `--user` flag:
```bash
pip install --user documorph
```

#### 3. SSL Certificate Errors
**Solution**: Update certificates:
```bash
# macOS
/Applications/Python\ 3.x/Install\ Certificates.command

# Linux
sudo apt-get update && sudo apt-get install ca-certificates
```

#### 4. Memory Errors with Large Files
**Solution**: Increase virtual memory or process smaller files:
```bash
documorph convert large_file.pdf --output-format html --max-file-size 25MB
```

### Getting Help

If you encounter issues:

1. **Check the logs**: DocuMorph creates logs in `logs/documorph.log`
2. **Run with verbose output**: `documorph --verbose convert file.pdf`
3. **Check system requirements**: Ensure Python 3.8+ is installed
4. **Update DocuMorph**: `pip install --upgrade documorph`
5. **Report issues**: [GitHub Issues](https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas/issues)

## Verification

After installation, verify DocuMorph is working correctly:

```bash
# Check version
documorph --version

# List supported formats
documorph formats

# Test with a simple file
echo "# Test Document" > test.md
documorph convert test.md --output-format html
```

## Uninstallation

To remove DocuMorph:

```bash
pip uninstall documorph
```

To remove all configuration files:
```bash
# Windows
rmdir /s "%APPDATA%\DocuMorph"

# macOS
rm -rf "~/Library/Application Support/DocuMorph"

# Linux
rm -rf ~/.config/DocuMorph
```

## Next Steps

After successful installation:

1. Read the [Usage Guide](usage.md)
2. Check out [Examples](../examples/)
3. Review [API Integration](api_integration.md)
4. Join the community discussions

---

For additional support, visit our [GitHub repository](https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas) or contact the development team.
