#!/usr/bin/env python3
"""
File Handler for DocuMorph

This module provides file handling utilities for reading, writing, and managing files.
"""

import os
import shutil
import tempfile
import logging
from pathlib import Path
from typing import List, Optional, Union, Dict, Any
import json
import yaml

logger = logging.getLogger(__name__)


class FileHandler:
    """
    File handling utilities
    """
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        Initialize the file handler
        
        Args:
            temp_dir: Optional custom temporary directory
        """
        self.temp_dir = Path(temp_dir) if temp_dir else Path(tempfile.gettempdir()) / "documorph"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"FileHandler initialized with temp dir: {self.temp_dir}")
    
    def read_file(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """
        Read file content
        
        Args:
            file_path: Path to the file
            encoding: File encoding
            
        Returns:
            File content as string
        """
        try:
            file_path = Path(file_path)
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                content = f.read()
            
            logger.debug(f"Read file: {file_path} ({len(content)} characters)")
            return content
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            raise
    
    def write_file(self, file_path: Union[str, Path], content: str, 
                   encoding: str = 'utf-8', create_dirs: bool = True) -> None:
        """
        Write content to file
        
        Args:
            file_path: Path to the file
            content: Content to write
            encoding: File encoding
            create_dirs: Create parent directories if they don't exist
        """
        try:
            file_path = Path(file_path)
            
            # Create parent directories if needed
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.info(f"Wrote file: {file_path} ({len(content)} characters)")
            
        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            raise
    
    def copy_file(self, src: Union[str, Path], dst: Union[str, Path], 
                  create_dirs: bool = True) -> None:
        """
        Copy file from source to destination
        
        Args:
            src: Source file path
            dst: Destination file path
            create_dirs: Create parent directories if they don't exist
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                raise FileNotFoundError(f"Source file not found: {src_path}")
            
            # Create parent directories if needed
            if create_dirs:
                dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
            logger.info(f"Copied file: {src_path} -> {dst_path}")
            
        except Exception as e:
            logger.error(f"Error copying file {src} to {dst}: {e}")
            raise
    
    def move_file(self, src: Union[str, Path], dst: Union[str, Path], 
                  create_dirs: bool = True) -> None:
        """
        Move file from source to destination
        
        Args:
            src: Source file path
            dst: Destination file path
            create_dirs: Create parent directories if they don't exist
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                raise FileNotFoundError(f"Source file not found: {src_path}")
            
            # Create parent directories if needed
            if create_dirs:
                dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src_path), str(dst_path))
            logger.info(f"Moved file: {src_path} -> {dst_path}")
            
        except Exception as e:
            logger.error(f"Error moving file {src} to {dst}: {e}")
            raise
    
    def delete_file(self, file_path: Union[str, Path]) -> None:
        """
        Delete a file
        
        Args:
            file_path: Path to the file to delete
        """
        try:
            file_path = Path(file_path)
            
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            raise
    
    def create_directory(self, dir_path: Union[str, Path], parents: bool = True) -> None:
        """
        Create directory
        
        Args:
            dir_path: Path to the directory
            parents: Create parent directories if they don't exist
        """
        try:
            dir_path = Path(dir_path)
            dir_path.mkdir(parents=parents, exist_ok=True)
            logger.info(f"Created directory: {dir_path}")
            
        except Exception as e:
            logger.error(f"Error creating directory {dir_path}: {e}")
            raise
    
    def list_files(self, directory: Union[str, Path], pattern: str = "*", 
                   recursive: bool = False) -> List[Path]:
        """
        List files in directory
        
        Args:
            directory: Directory to search
            pattern: File pattern (glob)
            recursive: Search recursively
            
        Returns:
            List of file paths
        """
        try:
            dir_path = Path(directory)
            
            if not dir_path.exists() or not dir_path.is_dir():
                logger.warning(f"Directory not found: {dir_path}")
                return []
            
            if recursive:
                files = list(dir_path.rglob(pattern))
            else:
                files = list(dir_path.glob(pattern))
            
            # Filter to only files (not directories)
            files = [f for f in files if f.is_file()]
            
            logger.debug(f"Found {len(files)} files in {dir_path}")
            return files
            
        except Exception as e:
            logger.error(f"Error listing files in {directory}: {e}")
            return []
    
    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get file information
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            stat = file_path.stat()
            
            info = {
                'path': str(file_path),
                'name': file_path.name,
                'stem': file_path.stem,
                'suffix': file_path.suffix,
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'accessed': stat.st_atime,
                'is_file': file_path.is_file(),
                'is_dir': file_path.is_dir(),
                'exists': file_path.exists()
            }
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            raise
    
    def create_temp_file(self, suffix: str = "", prefix: str = "documorph_", 
                        content: Optional[str] = None) -> Path:
        """
        Create temporary file
        
        Args:
            suffix: File suffix
            prefix: File prefix
            content: Optional content to write
            
        Returns:
            Path to temporary file
        """
        try:
            temp_file = tempfile.NamedTemporaryFile(
                suffix=suffix,
                prefix=prefix,
                dir=self.temp_dir,
                delete=False
            )
            
            temp_path = Path(temp_file.name)
            
            if content:
                with open(temp_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            temp_file.close()
            logger.debug(f"Created temp file: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Error creating temp file: {e}")
            raise
    
    def cleanup_temp_files(self, older_than_hours: int = 24) -> None:
        """
        Clean up old temporary files
        
        Args:
            older_than_hours: Remove files older than this many hours
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (older_than_hours * 3600)
            
            temp_files = self.list_files(self.temp_dir, recursive=True)
            removed_count = 0
            
            for temp_file in temp_files:
                try:
                    if temp_file.stat().st_mtime < cutoff_time:
                        temp_file.unlink()
                        removed_count += 1
                except Exception as e:
                    logger.warning(f"Error removing temp file {temp_file}: {e}")
            
            logger.info(f"Cleaned up {removed_count} temporary files")
            
        except Exception as e:
            logger.error(f"Error during temp file cleanup: {e}")
    
    def save_json(self, file_path: Union[str, Path], data: Dict[str, Any], 
                  indent: int = 2) -> None:
        """
        Save data as JSON file
        
        Args:
            file_path: Path to JSON file
            data: Data to save
            indent: JSON indentation
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False)
            
            logger.info(f"Saved JSON file: {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving JSON file {file_path}: {e}")
            raise
    
    def load_json(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load data from JSON file
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Loaded data
        """
        try:
            file_path = Path(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.debug(f"Loaded JSON file: {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"Error loading JSON file {file_path}: {e}")
            raise
    
    def save_yaml(self, file_path: Union[str, Path], data: Dict[str, Any]) -> None:
        """
        Save data as YAML file
        
        Args:
            file_path: Path to YAML file
            data: Data to save
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, indent=2)
            
            logger.info(f"Saved YAML file: {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving YAML file {file_path}: {e}")
            raise
    
    def load_yaml(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load data from YAML file
        
        Args:
            file_path: Path to YAML file
            
        Returns:
            Loaded data
        """
        try:
            file_path = Path(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            logger.debug(f"Loaded YAML file: {file_path}")
            return data or {}
            
        except Exception as e:
            logger.error(f"Error loading YAML file {file_path}: {e}")
            raise
    
    def ensure_output_directory(self, output_dir: Union[str, Path]) -> Path:
        """
        Ensure output directory exists
        
        Args:
            output_dir: Output directory path
            
        Returns:
            Path object for the directory
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"Ensured output directory: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error ensuring output directory {output_dir}: {e}")
            raise
