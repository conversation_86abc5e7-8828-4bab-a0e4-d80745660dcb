#!/usr/bin/env python3
"""
Unit tests for document parsers
"""

import unittest
import tempfile
from pathlib import Path

from documorph.parsers.txt_parser import TXTParser
from documorph.parsers.markdown_parser import MarkdownParser


class TestTXTParser(unittest.TestCase):
    """Test cases for TXT parser"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = TXTParser()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_parse_simple_text(self):
        """Test parsing simple text file"""
        # Create test file
        test_content = "This is a test document.\n\nIt has multiple paragraphs.\n\nAnd some structure."
        test_file = self.temp_dir / "test.txt"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Parse file
        content, metadata, structure = self.parser.parse(str(test_file))
        
        # Assertions
        self.assertIsInstance(content, str)
        self.assertIsInstance(metadata, dict)
        self.assertIsInstance(structure, dict)
        
        self.assertEqual(metadata['format'], 'txt')
        self.assertEqual(metadata['file_name'], 'test.txt')
        self.assertGreater(metadata['word_count'], 0)
        self.assertGreater(metadata['line_count'], 0)
    
    def test_encoding_detection(self):
        """Test encoding detection"""
        # Test UTF-8 content
        test_content = "Hello, 世界! This is a test with unicode characters: café, naïve, résumé"
        test_file = self.temp_dir / "unicode_test.txt"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        content, metadata, structure = self.parser.parse(str(test_file))
        
        self.assertIn('世界', content)
        self.assertIn('café', content)
        self.assertEqual(metadata['encoding'], 'utf-8')
    
    def test_structure_detection(self):
        """Test structure detection"""
        test_content = """MAIN TITLE

This is the introduction paragraph.

1. First Section

This is the content of the first section.

2. Second Section

This is the content of the second section.

- Bullet point one
- Bullet point two
- Bullet point three

> This is a quote

Conclusion paragraph."""
        
        test_file = self.temp_dir / "structured_test.txt"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        content, metadata, structure = self.parser.parse(str(test_file))
        
        # Check structure detection
        self.assertIn('headings', structure)
        self.assertIn('lists', structure)
        self.assertIn('quotes', structure)
        
        # Check headings
        headings = structure['headings']
        self.assertGreater(len(headings), 0)
        
        # Check lists
        lists = structure['lists']
        self.assertGreater(len(lists), 0)


class TestMarkdownParser(unittest.TestCase):
    """Test cases for Markdown parser"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = MarkdownParser()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_parse_markdown(self):
        """Test parsing markdown file"""
        test_content = """# Main Title

This is a **bold** paragraph with *italic* text and `code`.

## Section 1

Here's a [link](https://example.com) and an image:

![Alt text](image.png)

### Subsection

- List item 1
- List item 2
- List item 3

```python
def hello():
    print("Hello, world!")
```

> This is a blockquote

1. Numbered item 1
2. Numbered item 2
"""
        
        test_file = self.temp_dir / "test.md"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Parse file
        content, metadata, structure = self.parser.parse(str(test_file))
        
        # Assertions
        self.assertIsInstance(content, str)
        self.assertIsInstance(metadata, dict)
        self.assertIsInstance(structure, dict)
        
        self.assertEqual(metadata['format'], 'markdown')
        self.assertEqual(metadata['file_name'], 'test.md')
    
    def test_heading_extraction(self):
        """Test heading extraction"""
        test_content = """# Level 1 Heading

## Level 2 Heading

### Level 3 Heading

#### Level 4 Heading
"""
        
        test_file = self.temp_dir / "headings_test.md"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        content, metadata, structure = self.parser.parse(str(test_file))
        
        headings = structure['headings']
        self.assertEqual(len(headings), 4)
        
        # Check heading levels
        levels = [h['level'] for h in headings]
        self.assertEqual(levels, [1, 2, 3, 4])
    
    def test_link_extraction(self):
        """Test link extraction"""
        test_content = """# Links Test

Here's an [inline link](https://example.com).

Here's a [reference link][ref1].

Here's an auto link: <https://auto.example.com>

[ref1]: https://reference.example.com
"""
        
        test_file = self.temp_dir / "links_test.md"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        content, metadata, structure = self.parser.parse(str(test_file))
        
        links = structure['links']
        self.assertGreater(len(links), 0)
        
        # Check link types
        link_types = [link['type'] for link in links]
        self.assertIn('inline', link_types)
        self.assertIn('auto', link_types)
    
    def test_front_matter(self):
        """Test YAML front matter extraction"""
        test_content = """---
title: Test Document
author: Test Author
date: 2023-01-01
tags: test, markdown
---

# Main Content

This is the main content of the document.
"""
        
        test_file = self.temp_dir / "frontmatter_test.md"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        content, metadata, structure = self.parser.parse(str(test_file))
        
        # Check front matter extraction
        self.assertIn('front_matter', metadata)
        front_matter = metadata['front_matter']
        
        self.assertEqual(front_matter.get('title'), 'Test Document')
        self.assertEqual(front_matter.get('author'), 'Test Author')


if __name__ == '__main__':
    unittest.main()
