#!/usr/bin/env python3
"""
Markdown Parser for DocuMorph

This module handles parsing of Markdown documents.
"""

import logging
from typing import Dict, Any, Tuple, List
from pathlib import Path
import re

try:
    import markdown
    from markdown.extensions import toc, tables, codehilite, fenced_code
except ImportError:
    markdown = None

logger = logging.getLogger(__name__)


class MarkdownParser:
    """
    Parser for Markdown documents
    """
    
    description = "Markdown document parser with extension support"
    
    def __init__(self):
        """Initialize the Markdown parser"""
        if markdown is None:
            logger.warning("python-markdown not available, using basic parsing")
        
        # Configure markdown extensions
        self.extensions = [
            'toc',
            'tables', 
            'codehilite',
            'fenced_code',
            'nl2br',
            'sane_lists'
        ]
        
        logger.info("MarkdownParser initialized")
    
    def parse(self, file_path: str, **kwargs) -> Tuple[str, Dict[str, Any], Dict[str, Any]]:
        """
        Parse a Markdown document
        
        Args:
            file_path: Path to the Markdown file
            **kwargs: Additional parsing options
                - render_html: bool - Render to HTML (default: False)
                - extract_code_blocks: bool - Extract code blocks (default: True)
                - preserve_raw: bool - Preserve raw markdown (default: True)
        
        Returns:
            Tuple of (content, metadata, structure)
        """
        try:
            render_html = kwargs.get('render_html', False)
            extract_code_blocks = kwargs.get('extract_code_blocks', True)
            preserve_raw = kwargs.get('preserve_raw', True)
            
            logger.info(f"Parsing Markdown: {file_path}")
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='replace') as file:
                raw_content = file.read()
            
            # Extract metadata
            metadata = self._extract_metadata(file_path, raw_content)
            
            # Parse structure
            structure = self._analyze_structure(raw_content)
            
            # Extract code blocks if requested
            if extract_code_blocks:
                structure['code_blocks'] = self._extract_code_blocks(raw_content)
            
            # Process content
            if render_html and markdown:
                content = self._render_html(raw_content)
            else:
                content = raw_content if preserve_raw else self._strip_markdown(raw_content)
            
            logger.info(f"Successfully parsed Markdown with {len(structure['headings'])} headings")
            return content, metadata, structure
            
        except Exception as e:
            logger.error(f"Error parsing Markdown {file_path}: {e}")
            raise
    
    def _extract_metadata(self, file_path: str, content: str) -> Dict[str, Any]:
        """
        Extract metadata from Markdown file
        
        Args:
            file_path: Path to the Markdown file
            content: File content
            
        Returns:
            Dictionary containing metadata
        """
        file_path_obj = Path(file_path)
        lines = content.split('\n')
        
        metadata = {
            'file_path': file_path,
            'file_name': file_path_obj.name,
            'format': 'markdown',
            'line_count': len(lines),
            'word_count': len(content.split()),
            'char_count': len(content),
            'created': str(file_path_obj.stat().st_ctime),
            'modified': str(file_path_obj.stat().st_mtime)
        }
        
        # Extract YAML front matter if present
        front_matter = self._extract_front_matter(content)
        if front_matter:
            metadata['front_matter'] = front_matter
            metadata.update(front_matter)  # Merge front matter into metadata
        
        return metadata
    
    def _extract_front_matter(self, content: str) -> Dict[str, Any]:
        """
        Extract YAML front matter from Markdown content
        
        Args:
            content: Markdown content
            
        Returns:
            Dictionary with front matter data
        """
        front_matter = {}
        
        # Check for YAML front matter (--- at start and end)
        if content.startswith('---\n'):
            try:
                end_index = content.find('\n---\n', 4)
                if end_index != -1:
                    yaml_content = content[4:end_index]
                    
                    # Simple YAML parsing (basic key: value pairs)
                    for line in yaml_content.split('\n'):
                        line = line.strip()
                        if ':' in line and not line.startswith('#'):
                            key, value = line.split(':', 1)
                            key = key.strip()
                            value = value.strip().strip('"\'')
                            front_matter[key] = value
                            
            except Exception as e:
                logger.warning(f"Error parsing front matter: {e}")
        
        return front_matter
    
    def _analyze_structure(self, content: str) -> Dict[str, Any]:
        """
        Analyze Markdown document structure
        
        Args:
            content: Markdown content
            
        Returns:
            Dictionary with structure information
        """
        structure = {
            'headings': [],
            'links': [],
            'images': [],
            'lists': [],
            'tables': [],
            'code_blocks': [],
            'blockquotes': [],
            'emphasis': [],
            'line_breaks': []
        }
        
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Extract headings
            headings = self._extract_headings_from_line(line, line_num)
            structure['headings'].extend(headings)
            
            # Extract links
            links = self._extract_links_from_line(line, line_num)
            structure['links'].extend(links)
            
            # Extract images
            images = self._extract_images_from_line(line, line_num)
            structure['images'].extend(images)
            
            # Extract lists
            if self._is_list_item(line):
                list_item = {
                    'text': line.strip(),
                    'line_number': line_num,
                    'type': self._get_list_type(line),
                    'level': self._get_list_level(line)
                }
                structure['lists'].append(list_item)
            
            # Extract tables
            if '|' in line and line.strip():
                table_row = {
                    'text': line.strip(),
                    'line_number': line_num,
                    'columns': [col.strip() for col in line.split('|') if col.strip()]
                }
                structure['tables'].append(table_row)
            
            # Extract blockquotes
            if line.strip().startswith('>'):
                quote = {
                    'text': line.strip()[1:].strip(),
                    'line_number': line_num,
                    'level': len(line) - len(line.lstrip('>'))
                }
                structure['blockquotes'].append(quote)
            
            # Extract emphasis (bold, italic)
            emphasis = self._extract_emphasis_from_line(line, line_num)
            structure['emphasis'].extend(emphasis)
        
        return structure
    
    def _extract_headings_from_line(self, line: str, line_num: int) -> List[Dict[str, Any]]:
        """
        Extract headings from a line
        
        Args:
            line: Line content
            line_num: Line number
            
        Returns:
            List of heading dictionaries
        """
        headings = []
        
        # ATX-style headings (# ## ###)
        atx_match = re.match(r'^(#{1,6})\s+(.+)', line.strip())
        if atx_match:
            level = len(atx_match.group(1))
            text = atx_match.group(2).rstrip('#').strip()
            headings.append({
                'text': text,
                'level': level,
                'line_number': line_num,
                'style': 'atx'
            })
        
        # Setext-style headings (underlined with = or -)
        # This would need to be handled with context from previous line
        
        return headings
    
    def _extract_links_from_line(self, line: str, line_num: int) -> List[Dict[str, Any]]:
        """
        Extract links from a line
        
        Args:
            line: Line content
            line_num: Line number
            
        Returns:
            List of link dictionaries
        """
        links = []
        
        # Inline links [text](url)
        inline_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        for match in re.finditer(inline_pattern, line):
            links.append({
                'text': match.group(1),
                'url': match.group(2),
                'line_number': line_num,
                'type': 'inline',
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Reference links [text][ref]
        ref_pattern = r'\[([^\]]+)\]\[([^\]]*)\]'
        for match in re.finditer(ref_pattern, line):
            links.append({
                'text': match.group(1),
                'reference': match.group(2) or match.group(1),
                'line_number': line_num,
                'type': 'reference',
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Auto links <url>
        auto_pattern = r'<(https?://[^>]+)>'
        for match in re.finditer(auto_pattern, line):
            links.append({
                'text': match.group(1),
                'url': match.group(1),
                'line_number': line_num,
                'type': 'auto',
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        return links
    
    def _extract_images_from_line(self, line: str, line_num: int) -> List[Dict[str, Any]]:
        """
        Extract images from a line
        
        Args:
            line: Line content
            line_num: Line number
            
        Returns:
            List of image dictionaries
        """
        images = []
        
        # Inline images ![alt](src)
        inline_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        for match in re.finditer(inline_pattern, line):
            images.append({
                'alt_text': match.group(1),
                'src': match.group(2),
                'line_number': line_num,
                'type': 'inline',
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Reference images ![alt][ref]
        ref_pattern = r'!\[([^\]]*)\]\[([^\]]*)\]'
        for match in re.finditer(ref_pattern, line):
            images.append({
                'alt_text': match.group(1),
                'reference': match.group(2) or match.group(1),
                'line_number': line_num,
                'type': 'reference',
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        return images
    
    def _extract_emphasis_from_line(self, line: str, line_num: int) -> List[Dict[str, Any]]:
        """
        Extract emphasis (bold, italic) from a line
        
        Args:
            line: Line content
            line_num: Line number
            
        Returns:
            List of emphasis dictionaries
        """
        emphasis = []
        
        # Bold **text** or __text__
        bold_patterns = [r'\*\*([^*]+)\*\*', r'__([^_]+)__']
        for pattern in bold_patterns:
            for match in re.finditer(pattern, line):
                emphasis.append({
                    'text': match.group(1),
                    'type': 'bold',
                    'line_number': line_num,
                    'start_pos': match.start(),
                    'end_pos': match.end()
                })
        
        # Italic *text* or _text_
        italic_patterns = [r'\*([^*]+)\*', r'_([^_]+)_']
        for pattern in italic_patterns:
            for match in re.finditer(pattern, line):
                # Avoid matching bold patterns
                if not (match.start() > 0 and line[match.start()-1] in '*_') and \
                   not (match.end() < len(line) and line[match.end()] in '*_'):
                    emphasis.append({
                        'text': match.group(1),
                        'type': 'italic',
                        'line_number': line_num,
                        'start_pos': match.start(),
                        'end_pos': match.end()
                    })
        
        # Code `text`
        code_pattern = r'`([^`]+)`'
        for match in re.finditer(code_pattern, line):
            emphasis.append({
                'text': match.group(1),
                'type': 'code',
                'line_number': line_num,
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        return emphasis
    
    def _extract_code_blocks(self, content: str) -> List[Dict[str, Any]]:
        """
        Extract code blocks from content
        
        Args:
            content: Markdown content
            
        Returns:
            List of code block dictionaries
        """
        code_blocks = []
        
        # Fenced code blocks ```
        fenced_pattern = r'```(\w+)?\n(.*?)\n```'
        for match in re.finditer(fenced_pattern, content, re.DOTALL):
            language = match.group(1) or 'text'
            code = match.group(2)
            
            # Find line number
            line_num = content[:match.start()].count('\n') + 1
            
            code_blocks.append({
                'language': language,
                'code': code,
                'line_number': line_num,
                'type': 'fenced',
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Indented code blocks (4 spaces or 1 tab)
        lines = content.split('\n')
        in_code_block = False
        current_block = []
        block_start_line = 0
        
        for i, line in enumerate(lines):
            if line.startswith('    ') or line.startswith('\t'):
                if not in_code_block:
                    in_code_block = True
                    block_start_line = i + 1
                    current_block = []
                current_block.append(line[4:] if line.startswith('    ') else line[1:])
            else:
                if in_code_block and line.strip() == '':
                    current_block.append('')
                elif in_code_block:
                    # End of code block
                    code_blocks.append({
                        'language': 'text',
                        'code': '\n'.join(current_block),
                        'line_number': block_start_line,
                        'type': 'indented'
                    })
                    in_code_block = False
                    current_block = []
        
        # Handle code block at end of file
        if in_code_block and current_block:
            code_blocks.append({
                'language': 'text',
                'code': '\n'.join(current_block),
                'line_number': block_start_line,
                'type': 'indented'
            })
        
        return code_blocks
    
    def _is_list_item(self, line: str) -> bool:
        """
        Check if line is a list item
        
        Args:
            line: Line to check
            
        Returns:
            True if line is a list item
        """
        stripped = line.strip()
        
        # Unordered list
        if re.match(r'^[-*+]\s+', stripped):
            return True
        
        # Ordered list
        if re.match(r'^\d+\.\s+', stripped):
            return True
        
        return False
    
    def _get_list_type(self, line: str) -> str:
        """
        Get the type of list item
        
        Args:
            line: List item line
            
        Returns:
            List type ('ordered' or 'unordered')
        """
        stripped = line.strip()
        if re.match(r'^\d+\.\s+', stripped):
            return 'ordered'
        return 'unordered'
    
    def _get_list_level(self, line: str) -> int:
        """
        Get the nesting level of a list item
        
        Args:
            line: List item line
            
        Returns:
            Nesting level (0-based)
        """
        return (len(line) - len(line.lstrip())) // 2
    
    def _render_html(self, content: str) -> str:
        """
        Render Markdown to HTML
        
        Args:
            content: Markdown content
            
        Returns:
            HTML content
        """
        try:
            md = markdown.Markdown(extensions=self.extensions)
            html = md.convert(content)
            return html
        except Exception as e:
            logger.warning(f"Error rendering HTML: {e}")
            return content
    
    def _strip_markdown(self, content: str) -> str:
        """
        Strip Markdown formatting to get plain text
        
        Args:
            content: Markdown content
            
        Returns:
            Plain text content
        """
        # Remove headers
        content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)
        
        # Remove emphasis
        content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)  # Bold
        content = re.sub(r'\*([^*]+)\*', r'\1', content)      # Italic
        content = re.sub(r'__([^_]+)__', r'\1', content)      # Bold
        content = re.sub(r'_([^_]+)_', r'\1', content)        # Italic
        
        # Remove links
        content = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', content)  # Inline links
        content = re.sub(r'\[([^\]]+)\]\[[^\]]*\]', r'\1', content)  # Reference links
        
        # Remove images
        content = re.sub(r'!\[([^\]]*)\]\([^)]+\)', r'\1', content)  # Inline images
        content = re.sub(r'!\[([^\]]*)\]\[[^\]]*\]', r'\1', content)  # Reference images
        
        # Remove code
        content = re.sub(r'`([^`]+)`', r'\1', content)  # Inline code
        content = re.sub(r'```.*?\n(.*?)\n```', r'\1', content, flags=re.DOTALL)  # Code blocks
        
        # Remove blockquotes
        content = re.sub(r'^>\s*', '', content, flags=re.MULTILINE)
        
        # Remove list markers
        content = re.sub(r'^[-*+]\s+', '', content, flags=re.MULTILINE)
        content = re.sub(r'^\d+\.\s+', '', content, flags=re.MULTILINE)
        
        return content.strip()
