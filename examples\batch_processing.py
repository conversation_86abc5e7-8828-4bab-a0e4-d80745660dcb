#!/usr/bin/env python3
"""
Batch processing examples for DocuMorph
"""

import os
import time
from pathlib import Path
from documorph.core.document_parser import DocumentParser
from documorph.core.content_processor import ContentProcessor
from documorph.core.output_generator import OutputGenerator
from documorph.utils.file_handler import <PERSON>Handler
from documorph.utils.validators import FileValida<PERSON>


def create_sample_documents():
    """Create sample documents for batch processing"""
    print("=== Creating Sample Documents ===")
    
    # Create sample directory
    sample_dir = Path('batch_samples')
    sample_dir.mkdir(exist_ok=True)
    
    # Sample documents
    documents = [
        {
            'filename': 'technical_manual.md',
            'content': """# Technical Manual

## Overview
This is a technical manual for system administration.

## Installation
Follow these steps to install the system:
1. Download the installer
2. Run the installation wizard
3. Configure the settings

## Configuration
The system can be configured through the config file.

## Troubleshooting
Common issues and their solutions are listed here.
"""
        },
        {
            'filename': 'user_guide.txt',
            'content': """USER GUIDE

INTRODUCTION

This guide helps users understand the basic functionality.

GETTING STARTED

1. Create an account
2. Log in to the system
3. Navigate the interface

FEATURES

The system includes the following features:
- Document processing
- File management
- User administration

SUPPORT

For support, contact the help desk.
"""
        },
        {
            'filename': 'project_report.md',
            'content': """# Project Report

## Executive Summary
This report summarizes the project outcomes and recommendations.

## Methodology
The project used agile development methodologies.

## Results
Key findings include:
- Improved efficiency by 25%
- Reduced processing time
- Enhanced user satisfaction

## Recommendations
1. Continue current approach
2. Expand to other departments
3. Monitor performance metrics

## Conclusion
The project was successful and should be expanded.
"""
        },
        {
            'filename': 'meeting_notes.txt',
            'content': """MEETING NOTES

Date: 2024-01-15
Attendees: Team leads and project managers

AGENDA

1. Project status update
2. Budget review
3. Timeline discussion
4. Next steps

DISCUSSION POINTS

Project is on track for Q2 delivery.
Budget is within allocated limits.
Timeline may need adjustment for testing phase.

ACTION ITEMS

- Update project timeline
- Schedule testing resources
- Prepare status report

NEXT MEETING

Scheduled for next week to review progress.
"""
        },
        {
            'filename': 'api_documentation.md',
            'content': """# API Documentation

## Authentication
All API requests require authentication using API keys.

## Endpoints

### GET /users
Retrieve list of users.

**Parameters:**
- limit: Number of results (default: 10)
- offset: Starting position (default: 0)

**Response:**
```json
{
  "users": [...],
  "total": 100
}
```

### POST /users
Create a new user.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

## Error Handling
The API returns standard HTTP status codes.

## Rate Limiting
Requests are limited to 1000 per hour per API key.
"""
        }
    ]
    
    # Create sample files
    created_files = []
    for doc in documents:
        file_path = sample_dir / doc['filename']
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(doc['content'])
        created_files.append(file_path)
        print(f"  Created: {file_path}")
    
    print(f"Created {len(created_files)} sample documents in {sample_dir}")
    return sample_dir, created_files


def basic_batch_processing():
    """Demonstrate basic batch processing"""
    print("\n=== Basic Batch Processing ===")
    
    # Create sample documents
    sample_dir, sample_files = create_sample_documents()
    
    try:
        # Initialize components
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        file_handler = FileHandler()
        
        # Get all files in directory
        files_to_process = file_handler.list_files(sample_dir, pattern="*")
        print(f"\nFound {len(files_to_process)} files to process")
        
        # Process each file
        results = []
        start_time = time.time()
        
        for i, file_path in enumerate(files_to_process, 1):
            print(f"\n[{i}/{len(files_to_process)}] Processing: {file_path.name}")
            
            try:
                # Parse document
                parsed_doc = parser.parse(str(file_path))
                print(f"  - Parsed {parsed_doc.format} document")
                
                # Process content
                processed_content = processor.process(parsed_doc)
                print(f"  - Found {len(processed_content.sections)} sections")
                print(f"  - Reading time: {processed_content.reading_time} minutes")
                
                # Generate HTML output
                html_output = generator.generate(
                    processed_content,
                    'html',
                    './batch_output'
                )
                print(f"  - Generated HTML: {Path(html_output).name}")
                
                results.append({
                    'file': file_path.name,
                    'status': 'success',
                    'output': html_output,
                    'sections': len(processed_content.sections),
                    'reading_time': processed_content.reading_time
                })
                
            except Exception as e:
                print(f"  - Error: {e}")
                results.append({
                    'file': file_path.name,
                    'status': 'error',
                    'error': str(e)
                })
        
        # Summary
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n=== Batch Processing Summary ===")
        print(f"Total files: {len(files_to_process)}")
        print(f"Processing time: {processing_time:.2f} seconds")
        print(f"Average time per file: {processing_time/len(files_to_process):.2f} seconds")
        
        successful = [r for r in results if r['status'] == 'success']
        failed = [r for r in results if r['status'] == 'error']
        
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
        
        if successful:
            total_sections = sum(r['sections'] for r in successful)
            total_reading_time = sum(r['reading_time'] for r in successful)
            print(f"Total sections processed: {total_sections}")
            print(f"Total reading time: {total_reading_time} minutes")
        
        if failed:
            print("\nFailed files:")
            for result in failed:
                print(f"  - {result['file']}: {result['error']}")
        
    finally:
        # Clean up sample files
        import shutil
        if sample_dir.exists():
            shutil.rmtree(sample_dir)


def filtered_batch_processing():
    """Demonstrate filtered batch processing"""
    print("\n=== Filtered Batch Processing ===")
    
    # Create sample documents
    sample_dir, sample_files = create_sample_documents()
    
    try:
        file_handler = FileHandler()
        validator = FileValidator()
        
        # Filter by file extension
        print("\nFiltering by file extension:")
        md_files = file_handler.list_files(sample_dir, pattern="*.md")
        txt_files = file_handler.list_files(sample_dir, pattern="*.txt")
        
        print(f"  Markdown files: {len(md_files)}")
        for f in md_files:
            print(f"    - {f.name}")
        
        print(f"  Text files: {len(txt_files)}")
        for f in txt_files:
            print(f"    - {f.name}")
        
        # Validate files
        print("\nValidating files:")
        all_files = file_handler.list_files(sample_dir)
        valid_files = validator.validate_batch_files(all_files)
        
        print(f"  Total files: {len(all_files)}")
        print(f"  Valid files: {len(valid_files)}")
        
        # Process only markdown files
        print(f"\nProcessing only Markdown files...")
        
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        
        for file_path in md_files:
            print(f"\nProcessing: {file_path.name}")
            
            try:
                parsed_doc = parser.parse(str(file_path))
                processed_content = processor.process(parsed_doc)
                
                # Generate multiple outputs
                outputs = generator.generate_multiple(
                    processed_content,
                    ['html', 'quiz'],
                    './filtered_output'
                )
                
                for format_type, output_path in outputs.items():
                    print(f"  - {format_type}: {Path(output_path).name}")
                    
            except Exception as e:
                print(f"  - Error: {e}")
    
    finally:
        # Clean up
        import shutil
        if sample_dir.exists():
            shutil.rmtree(sample_dir)


def parallel_batch_processing():
    """Demonstrate parallel batch processing"""
    print("\n=== Parallel Batch Processing ===")
    
    # Create sample documents
    sample_dir, sample_files = create_sample_documents()
    
    try:
        import concurrent.futures
        import threading
        
        def process_single_file(file_path):
            """Process a single file"""
            thread_id = threading.current_thread().ident
            print(f"[Thread {thread_id}] Processing: {file_path.name}")
            
            try:
                parser = DocumentParser()
                processor = ContentProcessor()
                generator = OutputGenerator()
                
                # Parse and process
                parsed_doc = parser.parse(str(file_path))
                processed_content = processor.process(parsed_doc)
                
                # Generate output
                output_path = generator.generate(
                    processed_content,
                    'html',
                    './parallel_output'
                )
                
                return {
                    'file': file_path.name,
                    'status': 'success',
                    'output': output_path,
                    'thread_id': thread_id
                }
                
            except Exception as e:
                return {
                    'file': file_path.name,
                    'status': 'error',
                    'error': str(e),
                    'thread_id': thread_id
                }
        
        # Process files in parallel
        file_handler = FileHandler()
        files_to_process = file_handler.list_files(sample_dir)
        
        print(f"Processing {len(files_to_process)} files in parallel...")
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(process_single_file, file_path): file_path 
                for file_path in files_to_process
            }
            
            # Collect results
            results = []
            for future in concurrent.futures.as_completed(future_to_file):
                result = future.result()
                results.append(result)
                
                status_icon = "✅" if result['status'] == 'success' else "❌"
                print(f"{status_icon} {result['file']} (Thread {result['thread_id']})")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\nParallel processing completed in {processing_time:.2f} seconds")
        
        successful = [r for r in results if r['status'] == 'success']
        failed = [r for r in results if r['status'] == 'error']
        
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
        
    except ImportError:
        print("concurrent.futures not available, skipping parallel processing demo")
    
    finally:
        # Clean up
        import shutil
        if sample_dir.exists():
            shutil.rmtree(sample_dir)


def batch_processing_with_monitoring():
    """Demonstrate batch processing with progress monitoring"""
    print("\n=== Batch Processing with Monitoring ===")
    
    # Create sample documents
    sample_dir, sample_files = create_sample_documents()
    
    try:
        file_handler = FileHandler()
        files_to_process = file_handler.list_files(sample_dir)
        
        print(f"Processing {len(files_to_process)} files with progress monitoring...")
        
        # Initialize components
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        
        # Progress tracking
        total_files = len(files_to_process)
        processed_files = 0
        total_sections = 0
        total_words = 0
        
        start_time = time.time()
        
        for i, file_path in enumerate(files_to_process):
            # Calculate progress
            progress = (i / total_files) * 100
            
            print(f"\n[{progress:.1f}%] Processing {i+1}/{total_files}: {file_path.name}")
            
            try:
                # Parse document
                file_start_time = time.time()
                parsed_doc = parser.parse(str(file_path))
                
                # Process content
                processed_content = processor.process(parsed_doc)
                
                # Generate output
                output_path = generator.generate(
                    processed_content,
                    'html',
                    './monitored_output'
                )
                
                file_end_time = time.time()
                file_processing_time = file_end_time - file_start_time
                
                # Update statistics
                processed_files += 1
                total_sections += len(processed_content.sections)
                total_words += processed_content.metadata.get('word_count', 0)
                
                # Show file statistics
                print(f"  ✅ Completed in {file_processing_time:.2f}s")
                print(f"     Sections: {len(processed_content.sections)}")
                print(f"     Reading time: {processed_content.reading_time} min")
                print(f"     Difficulty: {processed_content.difficulty_level}")
                
                # Estimate remaining time
                if i > 0:
                    elapsed_time = time.time() - start_time
                    avg_time_per_file = elapsed_time / (i + 1)
                    remaining_files = total_files - (i + 1)
                    estimated_remaining = avg_time_per_file * remaining_files
                    
                    print(f"     ETA: {estimated_remaining:.1f}s remaining")
                
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        # Final summary
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n=== Final Summary ===")
        print(f"Total processing time: {total_time:.2f} seconds")
        print(f"Files processed: {processed_files}/{total_files}")
        print(f"Average time per file: {total_time/total_files:.2f} seconds")
        print(f"Total sections: {total_sections}")
        print(f"Total words: {total_words}")
        print(f"Processing rate: {total_words/total_time:.0f} words/second")
        
    finally:
        # Clean up
        import shutil
        if sample_dir.exists():
            shutil.rmtree(sample_dir)


if __name__ == '__main__':
    print("DocuMorph Batch Processing Examples")
    print("=" * 60)
    
    try:
        basic_batch_processing()
        filtered_batch_processing()
        parallel_batch_processing()
        batch_processing_with_monitoring()
        
        print("\n" + "=" * 60)
        print("Batch processing examples completed!")
        print("\nKey features demonstrated:")
        print("- Basic batch processing workflow")
        print("- File filtering and validation")
        print("- Parallel processing for performance")
        print("- Progress monitoring and statistics")
        
    except Exception as e:
        print(f"\n❌ Error in batch processing examples: {e}")
        import traceback
        traceback.print_exc()
