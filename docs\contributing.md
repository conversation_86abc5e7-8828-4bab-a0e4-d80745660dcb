# Contributing to DocuMorph

We welcome contributions to DocuMorph! This guide will help you get started with contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- **Be respectful**: Treat all contributors with respect and kindness
- **Be inclusive**: Welcome newcomers and help them get started
- **Be collaborative**: Work together to improve the project
- **Be constructive**: Provide helpful feedback and suggestions

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Git
- Basic knowledge of Python and CLI development
- Familiarity with document processing concepts

### Areas for Contribution

We welcome contributions in these areas:

1. **Core Features**
   - New document format support
   - Enhanced parsing algorithms
   - Output format improvements

2. **API Integrations**
   - Additional translation services
   - New TTS providers
   - Enhanced error handling

3. **User Experience**
   - CLI improvements
   - Better error messages
   - Performance optimizations

4. **Documentation**
   - Usage examples
   - API documentation
   - Tutorial content

5. **Testing**
   - Unit tests
   - Integration tests
   - Performance benchmarks

## Development Setup

### 1. Fork and Clone

```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/YOUR_USERNAME/DocuMorph_AugmentCode_GeminiCanvas.git
cd DocuMorph_AugmentCode_GeminiCanvas
```

### 2. Set Up Development Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install development dependencies
pip install -e .[dev]
```

### 3. Verify Setup

```bash
# Run tests to verify setup
python -m pytest tests/

# Run CLI to verify installation
documorph --help
```

### 4. Set Up Pre-commit Hooks

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install
```

## Contributing Guidelines

### Issue Reporting

Before creating a new issue:

1. **Search existing issues** to avoid duplicates
2. **Use issue templates** when available
3. **Provide detailed information**:
   - Operating system and Python version
   - DocuMorph version
   - Steps to reproduce
   - Expected vs actual behavior
   - Error messages and logs

### Feature Requests

When proposing new features:

1. **Check the roadmap** to see if it's already planned
2. **Describe the use case** and why it's needed
3. **Provide examples** of how it would work
4. **Consider implementation complexity**

### Bug Reports

For bug reports, include:

1. **Minimal reproduction case**
2. **Environment details**
3. **Error logs and stack traces**
4. **Expected behavior**

## Pull Request Process

### 1. Create a Branch

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Or bug fix branch
git checkout -b fix/issue-number-description
```

### 2. Make Changes

- Follow coding standards (see below)
- Add tests for new functionality
- Update documentation as needed
- Ensure all tests pass

### 3. Commit Changes

```bash
# Stage changes
git add .

# Commit with descriptive message
git commit -m "Add feature: description of what you added"
```

### 4. Push and Create PR

```bash
# Push to your fork
git push origin feature/your-feature-name

# Create pull request on GitHub
```

### 5. PR Requirements

Your pull request should:

- [ ] Have a clear title and description
- [ ] Reference related issues
- [ ] Include tests for new functionality
- [ ] Update documentation if needed
- [ ] Pass all CI checks
- [ ] Follow coding standards

## Coding Standards

### Python Style

We follow PEP 8 with these specific guidelines:

```python
# Use type hints
def parse_document(file_path: str) -> ParsedDocument:
    """Parse a document file."""
    pass

# Use descriptive variable names
document_content = parser.extract_text()
processed_sections = processor.analyze_structure()

# Use docstrings for all public functions
def generate_quiz(content: str, options: Dict[str, Any]) -> str:
    """
    Generate an interactive quiz from content.
    
    Args:
        content: The source content
        options: Generation options
        
    Returns:
        Path to generated quiz file
    """
    pass
```

### Code Organization

```
documorph/
├── core/           # Core functionality
├── parsers/        # Document parsers
├── generators/     # Output generators
├── apis/          # API integrations
└── utils/         # Utility functions
```

### Error Handling

```python
# Use specific exception types
try:
    result = api_call()
except ConnectionError as e:
    logger.error(f"Network error: {e}")
    raise DocumentProcessingError("Failed to connect to service")
except TimeoutError as e:
    logger.warning(f"Request timeout: {e}")
    # Implement retry logic
```

### Logging

```python
import logging

logger = logging.getLogger(__name__)

def process_document(file_path: str):
    logger.info(f"Processing document: {file_path}")
    try:
        # Process document
        logger.debug("Document processed successfully")
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise
```

## Testing

### Running Tests

```bash
# Run all tests
python -m pytest

# Run specific test file
python -m pytest tests/test_parsers.py

# Run with coverage
python -m pytest --cov=documorph --cov-report=html
```

### Writing Tests

```python
import unittest
from documorph.parsers.txt_parser import TXTParser

class TestTXTParser(unittest.TestCase):
    def setUp(self):
        self.parser = TXTParser()
    
    def test_parse_simple_text(self):
        # Test implementation
        content, metadata, structure = self.parser.parse("test.txt")
        self.assertIsInstance(content, str)
        self.assertEqual(metadata['format'], 'txt')
    
    def tearDown(self):
        # Cleanup if needed
        pass
```

### Test Categories

1. **Unit Tests**: Test individual functions and classes
2. **Integration Tests**: Test component interactions
3. **End-to-End Tests**: Test complete workflows
4. **Performance Tests**: Test speed and memory usage

## Documentation

### Code Documentation

- Use clear docstrings for all public functions
- Include type hints
- Provide usage examples
- Document complex algorithms

### User Documentation

- Update README.md for new features
- Add usage examples
- Update API documentation
- Create tutorials for complex features

### Documentation Style

```python
def convert_document(input_path: str, output_format: str, 
                    output_dir: str = "./output") -> str:
    """
    Convert a document to the specified format.
    
    This function handles the complete conversion workflow including
    parsing, processing, and output generation.
    
    Args:
        input_path: Path to the input document
        output_format: Target output format ('html', 'quiz', 'audio', 'translation')
        output_dir: Directory for output files (default: "./output")
    
    Returns:
        Path to the generated output file
    
    Raises:
        FileNotFoundError: If input file doesn't exist
        ValueError: If output format is not supported
        DocumentProcessingError: If conversion fails
    
    Example:
        >>> output_path = convert_document("doc.pdf", "html", "./web")
        >>> print(f"Generated: {output_path}")
    """
    pass
```

## Release Process

### Version Numbering

We use semantic versioning (SemVer):
- **Major**: Breaking changes
- **Minor**: New features, backward compatible
- **Patch**: Bug fixes, backward compatible

### Release Checklist

- [ ] Update version number
- [ ] Update CHANGELOG.md
- [ ] Run full test suite
- [ ] Update documentation
- [ ] Create release notes
- [ ] Tag release in Git

## Getting Help

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Email**: For security issues or private matters

### Mentorship

New contributors can get help from:
- Project maintainers
- Experienced contributors
- Community members

### Resources

- [Python Documentation](https://docs.python.org/)
- [Click Documentation](https://click.palletsprojects.com/)
- [pytest Documentation](https://docs.pytest.org/)

## Recognition

Contributors are recognized through:
- GitHub contributor list
- Release notes mentions
- Project documentation credits

## License

By contributing to DocuMorph, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to DocuMorph! Your efforts help make document transformation accessible to everyone.
