<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <meta name="author" content="{{ author }}">
    <meta name="keywords" content="{{ keywords|join(', ') }}">
    <meta name="description" content="{{ summary[:160] }}">
    <link rel="stylesheet" href="{{ title|replace(' ', '_')|lower }}.css">
</head>
<body>
    <header class="document-header">
        <h1 class="document-title">{{ title }}</h1>
        {% if author %}
        <p class="document-author">By {{ author }}</p>
        {% endif %}
        <div class="document-meta">
            <span class="reading-time">{{ reading_time }} min read</span>
            <span class="difficulty">Difficulty: {{ difficulty_level }}</span>
            <span class="generated-date">Generated: {{ generated_date }}</span>
        </div>
    </header>
    
    <main class="document-main">
        {% if options.include_toc and toc %}
        <nav class="toc">
            <h2>Table of Contents</h2>
            <ul>
                {% for item in toc %}
                <li class="toc-level-{{ item.level }}">
                    <a href="#{{ item.anchor }}">{{ item.text }}</a>
                </li>
                {% endfor %}
            </ul>
        </nav>
        {% endif %}
        
        {% if summary %}
        <section class="document-summary">
            <h2>Summary</h2>
            <p>{{ summary }}</p>
        </section>
        {% endif %}
        
        <div class="document-content">
            {% if sections %}
                {% for section in sections %}
                <section class="content-section">
                    <h{{ section.level }} id="{{ section.title|replace(' ', '-')|lower }}">{{ section.title }}</h{{ section.level }}>
                    <div class="section-content">{{ section.content|safe }}</div>
                </section>
                {% endfor %}
            {% else %}
                <div class="main-content">{{ content|safe }}</div>
            {% endif %}
        </div>
    </main>
    
    <footer class="document-footer">
        <p>Generated by DocuMorph</p>
    </footer>
    
    <script src="{{ title|replace(' ', '_')|lower }}.js"></script>
</body>
</html>
