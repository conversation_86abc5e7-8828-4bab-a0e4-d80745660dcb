#!/usr/bin/env python3
"""
DOCX Parser for DocuMorph

This module handles parsing of DOCX documents using python-docx.
"""

import logging
from typing import Dict, Any, Tuple, List
from pathlib import Path
import re

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_COLOR_INDEX
except ImportError:
    Document = None

logger = logging.getLogger(__name__)


class DOCXParser:
    """
    Parser for DOCX documents
    """
    
    description = "DOCX document parser using python-docx"
    
    def __init__(self):
        """Initialize the DOCX parser"""
        if Document is None:
            raise ImportError("python-docx is required for DOCX parsing. Install with: pip install python-docx")
        
        logger.info("DOCXParser initialized")
    
    def parse(self, file_path: str, **kwargs) -> Tuple[str, Dict[str, Any], Dict[str, Any]]:
        """
        Parse a DOCX document
        
        Args:
            file_path: Path to the DOCX file
            **kwargs: Additional parsing options
                - extract_images: bool - Extract images (default: False)
                - extract_tables: bool - Extract tables (default: True)
                - preserve_formatting: bool - Preserve text formatting (default: True)
        
        Returns:
            Tuple of (content, metadata, structure)
        """
        try:
            extract_images = kwargs.get('extract_images', False)
            extract_tables = kwargs.get('extract_tables', True)
            preserve_formatting = kwargs.get('preserve_formatting', True)
            
            logger.info(f"Parsing DOCX: {file_path}")
            
            # Load document
            doc = Document(file_path)
            
            # Extract metadata
            metadata = self._extract_metadata(doc, file_path)
            
            # Extract content and structure
            content, structure = self._extract_content(
                doc,
                extract_tables=extract_tables,
                preserve_formatting=preserve_formatting
            )
            
            # Extract images if requested
            if extract_images:
                structure['images'] = self._extract_images(doc)
            
            logger.info(f"Successfully parsed DOCX with {len(doc.paragraphs)} paragraphs")
            return content, metadata, structure
            
        except Exception as e:
            logger.error(f"Error parsing DOCX {file_path}: {e}")
            raise
    
    def _extract_metadata(self, doc: Document, file_path: str) -> Dict[str, Any]:
        """
        Extract metadata from DOCX document
        
        Args:
            doc: python-docx Document object
            file_path: Path to the DOCX file
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            'file_path': file_path,
            'file_name': Path(file_path).name,
            'format': 'docx',
            'paragraphs': len(doc.paragraphs),
            'tables': len(doc.tables),
            'sections': len(doc.sections)
        }
        
        # Extract core properties
        core_props = doc.core_properties
        if core_props:
            metadata.update({
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'keywords': core_props.keywords or '',
                'comments': core_props.comments or '',
                'category': core_props.category or '',
                'created': str(core_props.created) if core_props.created else '',
                'modified': str(core_props.modified) if core_props.modified else '',
                'last_modified_by': core_props.last_modified_by or '',
                'revision': core_props.revision or 0
            })
        
        return metadata
    
    def _extract_content(self, doc: Document, 
                        extract_tables: bool = True,
                        preserve_formatting: bool = True) -> Tuple[str, Dict[str, Any]]:
        """
        Extract content from DOCX document
        
        Args:
            doc: python-docx Document object
            extract_tables: Whether to extract table content
            preserve_formatting: Whether to preserve text formatting
            
        Returns:
            Tuple of (content_text, structure_info)
        """
        content_parts = []
        structure = {
            'headings': [],
            'paragraphs': [],
            'tables': [],
            'lists': [],
            'styles': set(),
            'hyperlinks': []
        }
        
        # Process paragraphs
        for i, paragraph in enumerate(doc.paragraphs):
            if paragraph.text.strip():
                # Analyze paragraph
                para_info = self._analyze_paragraph(paragraph, i)
                structure['paragraphs'].append(para_info)
                
                # Check if it's a heading
                if self._is_heading(paragraph):
                    heading_info = {
                        'text': paragraph.text.strip(),
                        'level': self._get_heading_level(paragraph),
                        'style': paragraph.style.name if paragraph.style else '',
                        'paragraph_index': i
                    }
                    structure['headings'].append(heading_info)
                
                # Extract hyperlinks
                hyperlinks = self._extract_hyperlinks(paragraph)
                structure['hyperlinks'].extend(hyperlinks)
                
                # Add to content
                if preserve_formatting:
                    formatted_text = self._format_paragraph_text(paragraph)
                    content_parts.append(formatted_text)
                else:
                    content_parts.append(paragraph.text.strip())
                
                # Track styles
                if paragraph.style:
                    structure['styles'].add(paragraph.style.name)
        
        # Process tables
        if extract_tables:
            for i, table in enumerate(doc.tables):
                table_content, table_info = self._extract_table(table, i)
                structure['tables'].append(table_info)
                content_parts.append(table_content)
        
        # Combine content
        full_content = '\n\n'.join(content_parts)
        
        # Convert styles set to list for JSON serialization
        structure['styles'] = list(structure['styles'])
        
        return full_content, structure
    
    def _analyze_paragraph(self, paragraph, index: int) -> Dict[str, Any]:
        """
        Analyze a paragraph for structure information
        
        Args:
            paragraph: python-docx Paragraph object
            index: Paragraph index
            
        Returns:
            Dictionary with paragraph information
        """
        return {
            'index': index,
            'text': paragraph.text.strip(),
            'style': paragraph.style.name if paragraph.style else '',
            'alignment': str(paragraph.alignment) if paragraph.alignment else '',
            'word_count': len(paragraph.text.split()),
            'char_count': len(paragraph.text),
            'is_heading': self._is_heading(paragraph),
            'has_formatting': self._has_formatting(paragraph)
        }
    
    def _is_heading(self, paragraph) -> bool:
        """
        Check if a paragraph is a heading
        
        Args:
            paragraph: python-docx Paragraph object
            
        Returns:
            True if paragraph is a heading
        """
        if paragraph.style and 'heading' in paragraph.style.name.lower():
            return True
        
        # Check for heading-like formatting
        if paragraph.runs:
            first_run = paragraph.runs[0]
            if (first_run.bold and 
                len(paragraph.text.strip()) < 100 and
                paragraph.text.strip()):
                return True
        
        return False
    
    def _get_heading_level(self, paragraph) -> int:
        """
        Get the heading level of a paragraph
        
        Args:
            paragraph: python-docx Paragraph object
            
        Returns:
            Heading level (1-6)
        """
        if paragraph.style:
            style_name = paragraph.style.name.lower()
            if 'heading' in style_name:
                # Extract number from style name like "Heading 1"
                import re
                match = re.search(r'heading\s*(\d+)', style_name)
                if match:
                    return int(match.group(1))
        
        return 1  # Default to level 1
    
    def _has_formatting(self, paragraph) -> bool:
        """
        Check if paragraph has special formatting
        
        Args:
            paragraph: python-docx Paragraph object
            
        Returns:
            True if paragraph has formatting
        """
        for run in paragraph.runs:
            if (run.bold or run.italic or run.underline or 
                run.font.color or run.font.highlight_color):
                return True
        return False
    
    def _format_paragraph_text(self, paragraph) -> str:
        """
        Format paragraph text preserving basic formatting
        
        Args:
            paragraph: python-docx Paragraph object
            
        Returns:
            Formatted text string
        """
        formatted_parts = []
        
        for run in paragraph.runs:
            text = run.text
            if not text:
                continue
            
            # Apply basic markdown-style formatting
            if run.bold:
                text = f"**{text}**"
            if run.italic:
                text = f"*{text}*"
            if run.underline:
                text = f"_{text}_"
            
            formatted_parts.append(text)
        
        return ''.join(formatted_parts)
    
    def _extract_hyperlinks(self, paragraph) -> List[Dict[str, Any]]:
        """
        Extract hyperlinks from a paragraph
        
        Args:
            paragraph: python-docx Paragraph object
            
        Returns:
            List of hyperlink dictionaries
        """
        hyperlinks = []
        
        try:
            # This is a simplified approach - full hyperlink extraction is complex
            for run in paragraph.runs:
                if hasattr(run, 'hyperlink') and run.hyperlink:
                    hyperlinks.append({
                        'text': run.text,
                        'url': run.hyperlink.address if run.hyperlink.address else '',
                        'type': 'hyperlink'
                    })
        except Exception as e:
            logger.warning(f"Error extracting hyperlinks: {e}")
        
        return hyperlinks
    
    def _extract_table(self, table, table_index: int) -> Tuple[str, Dict[str, Any]]:
        """
        Extract content from a table
        
        Args:
            table: python-docx Table object
            table_index: Table index
            
        Returns:
            Tuple of (table_content, table_info)
        """
        rows_data = []
        table_text_parts = []
        
        for row_index, row in enumerate(table.rows):
            row_data = []
            row_text_parts = []
            
            for cell_index, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                row_data.append(cell_text)
                row_text_parts.append(cell_text)
            
            rows_data.append(row_data)
            table_text_parts.append(' | '.join(row_text_parts))
        
        # Create table content
        table_content = '\n'.join(table_text_parts)
        
        # Create table info
        table_info = {
            'index': table_index,
            'rows': len(table.rows),
            'columns': len(table.columns) if table.rows else 0,
            'data': rows_data,
            'has_header': self._table_has_header(table)
        }
        
        return table_content, table_info
    
    def _table_has_header(self, table) -> bool:
        """
        Check if table has a header row
        
        Args:
            table: python-docx Table object
            
        Returns:
            True if table appears to have a header
        """
        if not table.rows:
            return False
        
        # Simple heuristic: check if first row cells have bold formatting
        first_row = table.rows[0]
        for cell in first_row.cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    if run.bold:
                        return True
        
        return False
    
    def _extract_images(self, doc: Document) -> List[Dict[str, Any]]:
        """
        Extract image information from DOCX document
        
        Args:
            doc: python-docx Document object
            
        Returns:
            List of image information dictionaries
        """
        images = []
        
        try:
            # This is a simplified approach - full image extraction requires
            # working with the underlying XML structure
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    images.append({
                        'type': 'image',
                        'target': rel.target_ref,
                        'relationship_id': rel.rId
                    })
        except Exception as e:
            logger.warning(f"Error extracting images: {e}")
        
        return images
