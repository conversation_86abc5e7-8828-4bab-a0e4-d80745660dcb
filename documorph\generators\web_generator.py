#!/usr/bin/env python3
"""
Web Generator for DocuMorph

This module generates responsive HTML/CSS web pages from processed content.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from jinja2 import Environment, FileSystemLoader, Template
except ImportError:
    Environment = FileSystemLoader = Template = None

from ..core.content_processor import ProcessedContent
from ..utils.file_handler import FileHandler
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class WebGenerator:
    """
    Generator for responsive HTML/CSS web pages
    """
    
    description = "Generates responsive HTML/CSS web pages with modern styling"
    supported_options = ['theme', 'responsive', 'include_toc', 'dark_mode', 'font_family']
    
    def __init__(self):
        """Initialize the web generator"""
        self.file_handler = FileHandler()
        self.template_dir = Path(__file__).parent.parent.parent / "templates" / "web"
        
        # Initialize Jinja2 environment if available
        if Environment and FileSystemLoader:
            self.jinja_env = Environment(loader=FileSystemLoader(str(self.template_dir)))
        else:
            self.jinja_env = None
            logger.warning("Jinja2 not available, using basic templates")
        
        logger.info("WebGenerator initialized")
    
    def generate(self, processed_content: ProcessedContent, output_dir: str, **kwargs) -> str:
        """
        Generate HTML/CSS web page
        
        Args:
            processed_content: ProcessedContent object
            output_dir: Output directory path
            **kwargs: Generation options
                - theme: str - Theme name (default: 'modern')
                - responsive: bool - Make responsive (default: True)
                - include_toc: bool - Include table of contents (default: True)
                - dark_mode: bool - Enable dark mode (default: False)
                - font_family: str - Font family (default: 'Inter, system-ui, sans-serif')
        
        Returns:
            Path to generated HTML file
        """
        try:
            # Get options
            theme = kwargs.get('theme', config_manager.get('output.html.theme', 'modern'))
            responsive = kwargs.get('responsive', config_manager.get('output.html.responsive', True))
            include_toc = kwargs.get('include_toc', config_manager.get('output.html.include_toc', True))
            dark_mode = kwargs.get('dark_mode', config_manager.get('output.html.dark_mode', False))
            font_family = kwargs.get('font_family', config_manager.get('output.html.font_family', 'Inter, system-ui, sans-serif'))
            
            logger.info(f"Generating HTML with theme: {theme}, responsive: {responsive}")
            
            # Prepare template data
            template_data = self._prepare_template_data(processed_content, {
                'theme': theme,
                'responsive': responsive,
                'include_toc': include_toc,
                'dark_mode': dark_mode,
                'font_family': font_family
            })
            
            # Generate HTML
            html_content = self._generate_html(template_data)
            
            # Generate CSS
            css_content = self._generate_css(template_data)
            
            # Generate JavaScript (if needed)
            js_content = self._generate_javascript(template_data)
            
            # Save files
            output_path = Path(output_dir)
            
            # Determine filename from metadata
            title = processed_content.metadata.get('title', 'document')
            safe_title = self._sanitize_filename(title)
            
            html_file = output_path / f"{safe_title}.html"
            css_file = output_path / f"{safe_title}.css"
            js_file = output_path / f"{safe_title}.js"
            
            # Write files
            self.file_handler.write_file(html_file, html_content)
            self.file_handler.write_file(css_file, css_content)
            
            if js_content:
                self.file_handler.write_file(js_file, js_content)
            
            logger.info(f"Generated web page: {html_file}")
            return str(html_file)
            
        except Exception as e:
            logger.error(f"Error generating web page: {e}")
            raise
    
    def _prepare_template_data(self, processed_content: ProcessedContent, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare data for template rendering
        
        Args:
            processed_content: ProcessedContent object
            options: Generation options
            
        Returns:
            Template data dictionary
        """
        # Build table of contents
        toc = []
        if options.get('include_toc', True):
            toc = self._build_toc(processed_content.structure.get('headings', []))
        
        # Format sections for HTML
        formatted_sections = []
        for section in processed_content.sections:
            formatted_sections.append({
                'title': section['title'],
                'content': self._format_content_for_html(section['content']),
                'level': section['level'],
                'word_count': section['word_count']
            })
        
        return {
            'title': processed_content.metadata.get('title', 'Document'),
            'author': processed_content.metadata.get('author', ''),
            'content': self._format_content_for_html(processed_content.content),
            'sections': formatted_sections,
            'toc': toc,
            'summary': processed_content.summary,
            'keywords': processed_content.keywords,
            'reading_time': processed_content.reading_time,
            'difficulty_level': processed_content.difficulty_level,
            'metadata': processed_content.metadata,
            'generated_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'options': options
        }
    
    def _build_toc(self, headings: list) -> list:
        """
        Build table of contents from headings
        
        Args:
            headings: List of heading dictionaries
            
        Returns:
            Table of contents structure
        """
        toc = []
        
        for heading in headings:
            toc_item = {
                'text': heading.get('text', ''),
                'level': heading.get('level', 1),
                'anchor': self._create_anchor(heading.get('text', '')),
                'line_number': heading.get('line_number', 0)
            }
            toc.append(toc_item)
        
        return toc
    
    def _create_anchor(self, text: str) -> str:
        """
        Create URL anchor from text
        
        Args:
            text: Heading text
            
        Returns:
            URL-safe anchor string
        """
        import re
        
        # Convert to lowercase and replace spaces with hyphens
        anchor = text.lower().replace(' ', '-')
        
        # Remove non-alphanumeric characters except hyphens
        anchor = re.sub(r'[^a-z0-9\-]', '', anchor)
        
        # Remove multiple consecutive hyphens
        anchor = re.sub(r'-+', '-', anchor)
        
        # Remove leading/trailing hyphens
        anchor = anchor.strip('-')
        
        return anchor or 'section'
    
    def _format_content_for_html(self, content: str) -> str:
        """
        Format content for HTML display
        
        Args:
            content: Raw content
            
        Returns:
            HTML-formatted content
        """
        import html
        
        # Escape HTML characters
        content = html.escape(content)
        
        # Convert line breaks to HTML
        content = content.replace('\n\n', '</p><p>')
        content = content.replace('\n', '<br>')
        
        # Wrap in paragraphs
        if content:
            content = f'<p>{content}</p>'
        
        # Simple markdown-like formatting
        import re
        
        # Bold text
        content = re.sub(r'\*\*([^*]+)\*\*', r'<strong>\1</strong>', content)
        
        # Italic text
        content = re.sub(r'\*([^*]+)\*', r'<em>\1</em>', content)
        
        # Code spans
        content = re.sub(r'`([^`]+)`', r'<code>\1</code>', content)
        
        return content
    
    def _generate_html(self, template_data: Dict[str, Any]) -> str:
        """
        Generate HTML content
        
        Args:
            template_data: Template data
            
        Returns:
            HTML content string
        """
        if self.jinja_env:
            try:
                template = self.jinja_env.get_template('base.html')
                return template.render(**template_data)
            except Exception as e:
                logger.warning(f"Error using Jinja2 template: {e}, falling back to basic template")
        
        # Fallback to basic template
        return self._generate_basic_html(template_data)
    
    def _generate_basic_html(self, data: Dict[str, Any]) -> str:
        """
        Generate basic HTML without Jinja2
        
        Args:
            data: Template data
            
        Returns:
            HTML content string
        """
        title = data['title']
        author = data['author']
        content = data['content']
        toc = data['toc']
        summary = data['summary']
        keywords = ', '.join(data['keywords'])
        reading_time = data['reading_time']
        difficulty = data['difficulty_level']
        generated_date = data['generated_date']
        
        # Build TOC HTML
        toc_html = ""
        if data['options'].get('include_toc', True) and toc:
            toc_html = "<nav class='toc'><h2>Table of Contents</h2><ul>"
            for item in toc:
                level_class = f"toc-level-{item['level']}"
                toc_html += f"<li class='{level_class}'><a href='#{item['anchor']}'>{item['text']}</a></li>"
            toc_html += "</ul></nav>"
        
        # Build sections HTML
        sections_html = ""
        for section in data['sections']:
            anchor = self._create_anchor(section['title'])
            level = min(section['level'], 6)  # Limit to h6
            sections_html += f"""
            <section class="content-section">
                <h{level} id="{anchor}">{section['title']}</h{level}>
                <div class="section-content">{section['content']}</div>
            </section>
            """
        
        html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <meta name="author" content="{author}">
    <meta name="keywords" content="{keywords}">
    <meta name="description" content="{summary[:160]}">
    <link rel="stylesheet" href="{Path(title).stem}.css">
</head>
<body>
    <header class="document-header">
        <h1 class="document-title">{title}</h1>
        {f'<p class="document-author">By {author}</p>' if author else ''}
        <div class="document-meta">
            <span class="reading-time">{reading_time} min read</span>
            <span class="difficulty">Difficulty: {difficulty}</span>
            <span class="generated-date">Generated: {generated_date}</span>
        </div>
    </header>
    
    <main class="document-main">
        {toc_html}
        
        {f'<section class="document-summary"><h2>Summary</h2><p>{summary}</p></section>' if summary else ''}
        
        <div class="document-content">
            {sections_html if sections_html else f'<div class="main-content">{content}</div>'}
        </div>
    </main>
    
    <footer class="document-footer">
        <p>Generated by DocuMorph</p>
    </footer>
    
    <script src="{Path(title).stem}.js"></script>
</body>
</html>"""
        
        return html_template
    
    def _generate_css(self, template_data: Dict[str, Any]) -> str:
        """
        Generate CSS content
        
        Args:
            template_data: Template data
            
        Returns:
            CSS content string
        """
        options = template_data['options']
        theme = options.get('theme', 'modern')
        responsive = options.get('responsive', True)
        dark_mode = options.get('dark_mode', False)
        font_family = options.get('font_family', 'Inter, system-ui, sans-serif')
        
        # Base styles
        css = f"""
/* DocuMorph Generated Styles */
:root {{
    --font-family: {font_family};
    --max-width: {config_manager.get('output.html.max_width', '1200px')};
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --background-color: {'#1a1a1a' if dark_mode else '#ffffff'};
    --text-color: {'#e5e5e5' if dark_mode else '#1f2937'};
    --border-color: {'#374151' if dark_mode else '#e5e7eb'};
    --code-bg: {'#374151' if dark_mode else '#f3f4f6'};
}}

* {{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}}

body {{
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    font-size: 16px;
}}

.document-header {{
    text-align: center;
    padding: 2rem 1rem;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 2rem;
}}

.document-title {{
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}}

.document-author {{
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}}

.document-meta {{
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: var(--secondary-color);
}}

.document-main {{
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
}}

.toc {{
    background: var(--code-bg);
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}}

.toc h2 {{
    margin-bottom: 1rem;
    color: var(--primary-color);
}}

.toc ul {{
    list-style: none;
}}

.toc li {{
    margin: 0.5rem 0;
}}

.toc a {{
    color: var(--text-color);
    text-decoration: none;
    padding: 0.25rem 0;
    display: block;
    border-radius: 4px;
    transition: background-color 0.2s;
}}

.toc a:hover {{
    background-color: var(--border-color);
    padding-left: 0.5rem;
}}

.toc-level-1 {{ font-weight: 600; }}
.toc-level-2 {{ margin-left: 1rem; }}
.toc-level-3 {{ margin-left: 2rem; }}
.toc-level-4 {{ margin-left: 3rem; }}

.document-summary {{
    background: var(--code-bg);
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}}

.document-summary h2 {{
    color: var(--primary-color);
    margin-bottom: 1rem;
}}

.content-section {{
    margin-bottom: 2rem;
}}

.content-section h1,
.content-section h2,
.content-section h3,
.content-section h4,
.content-section h5,
.content-section h6 {{
    color: var(--primary-color);
    margin: 1.5rem 0 1rem 0;
    line-height: 1.3;
}}

.content-section h1 {{ font-size: 2rem; }}
.content-section h2 {{ font-size: 1.75rem; }}
.content-section h3 {{ font-size: 1.5rem; }}
.content-section h4 {{ font-size: 1.25rem; }}
.content-section h5 {{ font-size: 1.1rem; }}
.content-section h6 {{ font-size: 1rem; }}

.section-content p {{
    margin-bottom: 1rem;
    text-align: justify;
}}

.section-content code {{
    background: var(--code-bg);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}}

.section-content strong {{
    font-weight: 600;
    color: var(--primary-color);
}}

.section-content em {{
    font-style: italic;
    color: var(--secondary-color);
}}

.document-footer {{
    text-align: center;
    padding: 2rem 1rem;
    margin-top: 3rem;
    border-top: 1px solid var(--border-color);
    color: var(--secondary-color);
    font-size: 0.9rem;
}}
"""
        
        # Add responsive styles
        if responsive:
            css += """
/* Responsive Styles */
@media (max-width: 768px) {
    .document-title {
        font-size: 2rem;
    }
    
    .document-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .toc-level-2 { margin-left: 0.5rem; }
    .toc-level-3 { margin-left: 1rem; }
    .toc-level-4 { margin-left: 1.5rem; }
    
    .content-section h1 { font-size: 1.75rem; }
    .content-section h2 { font-size: 1.5rem; }
    .content-section h3 { font-size: 1.25rem; }
}

@media (max-width: 480px) {
    .document-main {
        padding: 0 0.5rem;
    }
    
    .document-title {
        font-size: 1.75rem;
    }
    
    .toc, .document-summary {
        padding: 1rem;
    }
}
"""
        
        return css
    
    def _generate_javascript(self, template_data: Dict[str, Any]) -> str:
        """
        Generate JavaScript content
        
        Args:
            template_data: Template data
            
        Returns:
            JavaScript content string
        """
        js = """
// DocuMorph Generated JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for TOC links
    const tocLinks = document.querySelectorAll('.toc a');
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add reading progress indicator
    const progressBar = document.createElement('div');
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: var(--primary-color);
        z-index: 1000;
        transition: width 0.1s;
    `;
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        progressBar.style.width = scrollPercent + '%';
    });
    
    console.log('DocuMorph web page loaded successfully');
});
"""
        
        return js
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for safe file system usage
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        import re
        
        # Replace spaces with underscores
        filename = filename.replace(' ', '_')
        
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        
        # Limit length
        if len(filename) > 50:
            filename = filename[:50]
        
        # Ensure it's not empty
        if not filename:
            filename = 'document'
        
        return filename.lower()
