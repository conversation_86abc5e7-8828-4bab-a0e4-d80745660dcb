#!/usr/bin/env python3
"""
Document Parser for DocuMorph

This module provides the main DocumentParser class that coordinates
parsing of different document formats (PDF, DOCX, TXT, MD).
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from ..parsers.pdf_parser import PDFParser
from ..parsers.docx_parser import DOCXParser
from ..parsers.txt_parser import TXTParser
from ..parsers.markdown_parser import MarkdownParser
from ..utils.validators import FileValidator
from ..utils.file_handler import FileHandler
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


@dataclass
class ParsedDocument:
    """Container for parsed document data"""
    content: str
    metadata: Dict[str, Any]
    structure: Dict[str, Any]
    format: str
    file_path: str
    file_size: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            'content': self.content,
            'metadata': self.metadata,
            'structure': self.structure,
            'format': self.format,
            'file_path': self.file_path,
            'file_size': self.file_size
        }


class DocumentParser:
    """
    Main document parser that handles multiple document formats
    """
    
    def __init__(self):
        """Initialize the document parser"""
        self.supported_formats = config_manager.get('processing.supported_formats', 
                                                   ['pdf', 'docx', 'txt', 'md'])
        self.max_file_size = config_manager.get('processing.max_file_size', '50MB')
        
        # Initialize format-specific parsers
        self.parsers = {
            'pdf': PDFParser(),
            'docx': DOCXParser(),
            'txt': TXTParser(),
            'md': MarkdownParser()
        }
        
        # Initialize utilities
        self.validator = FileValidator()
        self.file_handler = FileHandler()
        
        logger.info("DocumentParser initialized with supported formats: %s", 
                   self.supported_formats)
    
    def parse(self, file_path: str, **kwargs) -> ParsedDocument:
        """
        Parse a document file
        
        Args:
            file_path: Path to the document file
            **kwargs: Additional parsing options
            
        Returns:
            ParsedDocument object containing parsed content and metadata
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: For parsing errors
        """
        try:
            # Validate file
            file_path = Path(file_path).resolve()
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Get file format
            file_format = self._detect_format(file_path)
            if file_format not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {file_format}")
            
            # Validate file size and content
            if not self.validator.validate_file(file_path, self.max_file_size):
                raise ValueError(f"File validation failed for: {file_path}")
            
            logger.info(f"Parsing {file_format.upper()} document: {file_path}")
            
            # Get appropriate parser
            parser = self.parsers[file_format]
            
            # Parse the document
            content, metadata, structure = parser.parse(str(file_path), **kwargs)
            
            # Create parsed document object
            parsed_doc = ParsedDocument(
                content=content,
                metadata=metadata,
                structure=structure,
                format=file_format,
                file_path=str(file_path),
                file_size=file_path.stat().st_size
            )
            
            logger.info(f"Successfully parsed document: {file_path}")
            return parsed_doc
            
        except Exception as e:
            logger.error(f"Error parsing document {file_path}: {e}")
            raise
    
    def parse_batch(self, file_paths: List[str], **kwargs) -> List[ParsedDocument]:
        """
        Parse multiple documents in batch
        
        Args:
            file_paths: List of file paths to parse
            **kwargs: Additional parsing options
            
        Returns:
            List of ParsedDocument objects
        """
        results = []
        errors = []
        
        logger.info(f"Starting batch parsing of {len(file_paths)} documents")
        
        for file_path in file_paths:
            try:
                parsed_doc = self.parse(file_path, **kwargs)
                results.append(parsed_doc)
            except Exception as e:
                error_info = {'file_path': file_path, 'error': str(e)}
                errors.append(error_info)
                logger.error(f"Failed to parse {file_path}: {e}")
        
        if errors:
            logger.warning(f"Batch parsing completed with {len(errors)} errors")
            # Optionally, you could raise an exception or return errors separately
        
        logger.info(f"Batch parsing completed: {len(results)} successful, {len(errors)} failed")
        return results
    
    def _detect_format(self, file_path: Path) -> str:
        """
        Detect document format from file extension
        
        Args:
            file_path: Path to the file
            
        Returns:
            Detected format string
        """
        extension = file_path.suffix.lower()
        
        format_mapping = {
            '.pdf': 'pdf',
            '.docx': 'docx',
            '.doc': 'docx',  # Treat .doc as .docx for now
            '.txt': 'txt',
            '.md': 'md',
            '.markdown': 'md'
        }
        
        detected_format = format_mapping.get(extension)
        if not detected_format:
            # Try to detect by content if extension is unknown
            detected_format = self._detect_format_by_content(file_path)
        
        return detected_format or 'txt'  # Default to txt if detection fails
    
    def _detect_format_by_content(self, file_path: Path) -> Optional[str]:
        """
        Detect format by analyzing file content
        
        Args:
            file_path: Path to the file
            
        Returns:
            Detected format or None
        """
        try:
            with open(file_path, 'rb') as f:
                header = f.read(1024)
            
            # PDF signature
            if header.startswith(b'%PDF'):
                return 'pdf'
            
            # DOCX signature (ZIP with specific structure)
            if header.startswith(b'PK') and b'word/' in header:
                return 'docx'
            
            # Try to read as text for markdown detection
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read(1000)
                
                # Simple markdown detection
                markdown_indicators = ['#', '##', '###', '**', '*', '`', '```', '[', '](']
                if any(indicator in content for indicator in markdown_indicators):
                    return 'md'
                
            except UnicodeDecodeError:
                pass
            
            return None
            
        except Exception as e:
            logger.warning(f"Error detecting format by content for {file_path}: {e}")
            return None
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats
        
        Returns:
            List of supported format strings
        """
        return self.supported_formats.copy()
    
    def is_supported_format(self, file_path: str) -> bool:
        """
        Check if a file format is supported
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if format is supported, False otherwise
        """
        try:
            file_format = self._detect_format(Path(file_path))
            return file_format in self.supported_formats
        except Exception:
            return False
    
    def get_parser_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about available parsers
        
        Returns:
            Dictionary with parser information
        """
        info = {}
        for format_name, parser in self.parsers.items():
            info[format_name] = {
                'class': parser.__class__.__name__,
                'supported': format_name in self.supported_formats,
                'description': getattr(parser, 'description', 'No description available')
            }
        return info
