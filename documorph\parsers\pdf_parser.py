#!/usr/bin/env python3
"""
PDF Parser for DocuMorph

This module handles parsing of PDF documents using PyPDF2.
"""

import logging
from typing import Dict, Any, Tuple, List
from pathlib import Path
import re

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

logger = logging.getLogger(__name__)


class PDFParser:
    """
    Parser for PDF documents
    """
    
    description = "PDF document parser using PyPDF2"
    
    def __init__(self):
        """Initialize the PDF parser"""
        if PyPDF2 is None:
            raise ImportError("PyPDF2 is required for PDF parsing. Install with: pip install PyPDF2")
        
        self.supported_versions = ["1.4", "1.5", "1.6", "1.7", "2.0"]
        logger.info("PDFParser initialized")
    
    def parse(self, file_path: str, **kwargs) -> Tuple[str, Dict[str, Any], Dict[str, Any]]:
        """
        Parse a PDF document
        
        Args:
            file_path: Path to the PDF file
            **kwargs: Additional parsing options
                - extract_images: bool - Extract images (default: False)
                - preserve_formatting: bool - Preserve text formatting (default: True)
                - page_range: tuple - (start, end) page range (default: None for all pages)
        
        Returns:
            Tuple of (content, metadata, structure)
        """
        try:
            extract_images = kwargs.get('extract_images', False)
            preserve_formatting = kwargs.get('preserve_formatting', True)
            page_range = kwargs.get('page_range', None)
            
            logger.info(f"Parsing PDF: {file_path}")
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Extract metadata
                metadata = self._extract_metadata(pdf_reader, file_path)
                
                # Extract content
                content, structure = self._extract_content(
                    pdf_reader, 
                    preserve_formatting=preserve_formatting,
                    page_range=page_range
                )
                
                # Extract images if requested
                if extract_images:
                    structure['images'] = self._extract_images(pdf_reader)
                
                logger.info(f"Successfully parsed PDF with {len(pdf_reader.pages)} pages")
                return content, metadata, structure
                
        except Exception as e:
            logger.error(f"Error parsing PDF {file_path}: {e}")
            raise
    
    def _extract_metadata(self, pdf_reader: PyPDF2.PdfReader, file_path: str) -> Dict[str, Any]:
        """
        Extract metadata from PDF
        
        Args:
            pdf_reader: PyPDF2 PdfReader object
            file_path: Path to the PDF file
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            'file_path': file_path,
            'file_name': Path(file_path).name,
            'format': 'pdf',
            'pages': len(pdf_reader.pages),
            'encrypted': pdf_reader.is_encrypted
        }
        
        # Extract PDF metadata if available
        if pdf_reader.metadata:
            pdf_meta = pdf_reader.metadata
            metadata.update({
                'title': pdf_meta.get('/Title', ''),
                'author': pdf_meta.get('/Author', ''),
                'subject': pdf_meta.get('/Subject', ''),
                'creator': pdf_meta.get('/Creator', ''),
                'producer': pdf_meta.get('/Producer', ''),
                'creation_date': str(pdf_meta.get('/CreationDate', '')),
                'modification_date': str(pdf_meta.get('/ModDate', ''))
            })
        
        return metadata
    
    def _extract_content(self, pdf_reader: PyPDF2.PdfReader, 
                        preserve_formatting: bool = True,
                        page_range: tuple = None) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text content from PDF
        
        Args:
            pdf_reader: PyPDF2 PdfReader object
            preserve_formatting: Whether to preserve text formatting
            page_range: Tuple of (start, end) page numbers (1-indexed)
            
        Returns:
            Tuple of (content_text, structure_info)
        """
        pages_content = []
        structure = {
            'pages': [],
            'headings': [],
            'paragraphs': [],
            'tables': [],
            'links': []
        }
        
        # Determine page range
        total_pages = len(pdf_reader.pages)
        if page_range:
            start_page = max(0, page_range[0] - 1)  # Convert to 0-indexed
            end_page = min(total_pages, page_range[1])
        else:
            start_page = 0
            end_page = total_pages
        
        for page_num in range(start_page, end_page):
            try:
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                
                if page_text.strip():
                    # Process page content
                    processed_text = self._process_page_text(page_text, preserve_formatting)
                    pages_content.append(processed_text)
                    
                    # Extract structure information
                    page_structure = self._analyze_page_structure(page_text, page_num + 1)
                    structure['pages'].append(page_structure)
                    
                    # Extract headings
                    headings = self._extract_headings(page_text, page_num + 1)
                    structure['headings'].extend(headings)
                    
                    # Extract links if available
                    if hasattr(page, 'annotations') and page.annotations:
                        links = self._extract_links(page, page_num + 1)
                        structure['links'].extend(links)
                
            except Exception as e:
                logger.warning(f"Error processing page {page_num + 1}: {e}")
                continue
        
        # Combine all pages
        full_content = '\n\n'.join(pages_content)
        
        # Post-process content
        full_content = self._post_process_content(full_content)
        
        return full_content, structure
    
    def _process_page_text(self, text: str, preserve_formatting: bool) -> str:
        """
        Process text from a single page
        
        Args:
            text: Raw text from page
            preserve_formatting: Whether to preserve formatting
            
        Returns:
            Processed text
        """
        if not preserve_formatting:
            # Simple cleanup
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
        else:
            # Preserve line breaks and spacing
            lines = text.split('\n')
            processed_lines = []
            
            for line in lines:
                line = line.strip()
                if line:
                    processed_lines.append(line)
            
            text = '\n'.join(processed_lines)
        
        return text
    
    def _analyze_page_structure(self, text: str, page_num: int) -> Dict[str, Any]:
        """
        Analyze the structure of a page
        
        Args:
            text: Page text
            page_num: Page number
            
        Returns:
            Dictionary with page structure information
        """
        lines = text.split('\n')
        
        return {
            'page_number': page_num,
            'line_count': len(lines),
            'word_count': len(text.split()),
            'char_count': len(text),
            'has_content': bool(text.strip())
        }
    
    def _extract_headings(self, text: str, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract potential headings from text
        
        Args:
            text: Page text
            page_num: Page number
            
        Returns:
            List of heading dictionaries
        """
        headings = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Simple heuristics for heading detection
            is_heading = False
            level = 1
            
            # All caps and short
            if line.isupper() and len(line) < 100:
                is_heading = True
                level = 1
            
            # Starts with number pattern
            elif re.match(r'^\d+\.?\s+[A-Z]', line):
                is_heading = True
                level = 2
            
            # Short line followed by empty line or end
            elif (len(line) < 80 and 
                  (i == len(lines) - 1 or not lines[i + 1].strip())):
                is_heading = True
                level = 3
            
            if is_heading:
                headings.append({
                    'text': line,
                    'level': level,
                    'page': page_num,
                    'line': i + 1
                })
        
        return headings
    
    def _extract_links(self, page, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract links from page annotations
        
        Args:
            page: PDF page object
            page_num: Page number
            
        Returns:
            List of link dictionaries
        """
        links = []
        
        try:
            if hasattr(page, 'annotations') and page.annotations:
                for annotation in page.annotations:
                    if annotation.get('/Subtype') == '/Link':
                        link_info = {
                            'page': page_num,
                            'type': 'link',
                            'url': annotation.get('/A', {}).get('/URI', ''),
                            'rect': annotation.get('/Rect', [])
                        }
                        links.append(link_info)
        except Exception as e:
            logger.warning(f"Error extracting links from page {page_num}: {e}")
        
        return links
    
    def _extract_images(self, pdf_reader: PyPDF2.PdfReader) -> List[Dict[str, Any]]:
        """
        Extract image information from PDF
        
        Args:
            pdf_reader: PyPDF2 PdfReader object
            
        Returns:
            List of image information dictionaries
        """
        images = []
        
        try:
            for page_num, page in enumerate(pdf_reader.pages):
                if '/XObject' in page['/Resources']:
                    xObject = page['/Resources']['/XObject'].get_object()
                    
                    for obj in xObject:
                        if xObject[obj]['/Subtype'] == '/Image':
                            image_info = {
                                'page': page_num + 1,
                                'name': obj,
                                'width': xObject[obj].get('/Width', 0),
                                'height': xObject[obj].get('/Height', 0),
                                'filter': xObject[obj].get('/Filter', ''),
                                'colorspace': xObject[obj].get('/ColorSpace', '')
                            }
                            images.append(image_info)
        except Exception as e:
            logger.warning(f"Error extracting images: {e}")
        
        return images
    
    def _post_process_content(self, content: str) -> str:
        """
        Post-process the extracted content
        
        Args:
            content: Raw extracted content
            
        Returns:
            Post-processed content
        """
        # Remove excessive whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # Fix common PDF extraction issues
        content = re.sub(r'([a-z])([A-Z])', r'\1 \2', content)  # Add space between words
        content = re.sub(r'(\w)-\s*\n\s*(\w)', r'\1\2', content)  # Fix hyphenated words
        
        return content.strip()
