#!/usr/bin/env python3
"""
Audio Generator for DocuMorph

This module generates audio podcasts from processed content using free TTS services.
"""

import logging
import os
from pathlib import Path
from typing import Dict, Any, List

try:
    from gtts import gTTS
    import pygame
except ImportError:
    gTTS = pygame = None

from ..core.content_processor import ProcessedContent
from ..utils.file_handler import FileHandler
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class AudioGenerator:
    """
    Generator for audio podcasts using TTS
    """
    
    description = "Generates audio podcasts using Google Text-to-Speech (free)"
    supported_options = ['voice', 'speed', 'format', 'add_intro', 'chapter_breaks']
    
    def __init__(self):
        """Initialize the audio generator"""
        self.file_handler = FileHandler()
        
        if gTTS is None:
            logger.warning("gTTS not available, audio generation will be limited")
        
        self.supported_languages = {
            'en': 'en',
            'es': 'es', 
            'fr': 'fr',
            'de': 'de',
            'it': 'it',
            'pt': 'pt',
            'ru': 'ru',
            'ja': 'ja',
            'ko': 'ko',
            'zh': 'zh'
        }
        
        logger.info("AudioGenerator initialized")
    
    def generate(self, processed_content: ProcessedContent, output_dir: str, **kwargs) -> str:
        """
        Generate audio podcast
        
        Args:
            processed_content: ProcessedContent object
            output_dir: Output directory path
            **kwargs: Generation options
                - voice: str - Voice language code (default: 'en')
                - speed: float - Speech speed (default: 1.0)
                - format: str - Audio format (default: 'mp3')
                - add_intro: bool - Add introduction (default: True)
                - chapter_breaks: bool - Add breaks between sections (default: True)
        
        Returns:
            Path to generated audio file
        """
        try:
            if gTTS is None:
                raise ImportError("gTTS is required for audio generation. Install with: pip install gtts")
            
            # Get options
            voice_lang = kwargs.get('voice', config_manager.get('output.audio.voice', 'en'))
            add_intro = kwargs.get('add_intro', config_manager.get('output.audio.add_intro', True))
            chapter_breaks = kwargs.get('chapter_breaks', config_manager.get('output.audio.chapter_breaks', True))
            
            # Map voice to supported language
            if voice_lang not in self.supported_languages:
                logger.warning(f"Unsupported voice language: {voice_lang}, using 'en'")
                voice_lang = 'en'
            
            logger.info(f"Generating audio in language: {voice_lang}")
            
            # Prepare text content
            text_content = self._prepare_text_for_speech(processed_content, add_intro, chapter_breaks)
            
            # Generate audio
            title = processed_content.metadata.get('title', 'document')
            safe_title = self._sanitize_filename(title)
            
            output_path = Path(output_dir)
            audio_file = output_path / f"{safe_title}_podcast.mp3"
            
            # Split text into chunks (gTTS has character limits)
            chunks = self._split_text_into_chunks(text_content)
            
            if len(chunks) == 1:
                # Single file
                tts = gTTS(text=chunks[0], lang=voice_lang, slow=False)
                tts.save(str(audio_file))
            else:
                # Multiple chunks - combine them
                temp_files = []
                
                for i, chunk in enumerate(chunks):
                    temp_file = self.file_handler.create_temp_file(
                        suffix=f"_chunk_{i}.mp3",
                        prefix="audio_"
                    )
                    
                    tts = gTTS(text=chunk, lang=voice_lang, slow=False)
                    tts.save(str(temp_file))
                    temp_files.append(temp_file)
                
                # Combine audio files (simplified - just concatenate)
                self._combine_audio_files(temp_files, audio_file)
                
                # Clean up temp files
                for temp_file in temp_files:
                    self.file_handler.delete_file(temp_file)
            
            # Generate metadata file
            metadata_file = output_path / f"{safe_title}_podcast_info.json"
            metadata = self._create_audio_metadata(processed_content, str(audio_file))
            self.file_handler.save_json(metadata_file, metadata)
            
            logger.info(f"Generated audio podcast: {audio_file}")
            return str(audio_file)
            
        except Exception as e:
            logger.error(f"Error generating audio: {e}")
            raise
    
    def _prepare_text_for_speech(self, processed_content: ProcessedContent, 
                                add_intro: bool, chapter_breaks: bool) -> str:
        """Prepare text content for speech synthesis"""
        text_parts = []
        
        # Add introduction
        if add_intro:
            title = processed_content.metadata.get('title', 'Document')
            author = processed_content.metadata.get('author', '')
            
            intro = f"Welcome to the audio version of {title}."
            if author:
                intro += f" By {author}."
            
            intro += f" This document has an estimated reading time of {processed_content.reading_time} minutes."
            
            if processed_content.summary:
                intro += f" Here's a brief summary: {processed_content.summary}"
            
            intro += " Let's begin."
            text_parts.append(intro)
        
        # Add sections
        for i, section in enumerate(processed_content.sections):
            if chapter_breaks and i > 0:
                text_parts.append("Next section.")
            
            # Add section title
            if section['title'] and section['title'].strip():
                text_parts.append(f"Section: {section['title']}")
            
            # Clean and add section content
            content = self._clean_text_for_speech(section['content'])
            if content:
                text_parts.append(content)
        
        # Add conclusion
        if add_intro:
            conclusion = "This concludes the audio version of the document. Thank you for listening."
            text_parts.append(conclusion)
        
        return " ".join(text_parts)
    
    def _clean_text_for_speech(self, text: str) -> str:
        """Clean text for better speech synthesis"""
        import re
        
        # Remove HTML tags if any
        text = re.sub(r'<[^>]+>', '', text)
        
        # Fix common abbreviations
        abbreviations = {
            'Dr.': 'Doctor',
            'Mr.': 'Mister', 
            'Mrs.': 'Missus',
            'Ms.': 'Miss',
            'Prof.': 'Professor',
            'etc.': 'etcetera',
            'e.g.': 'for example',
            'i.e.': 'that is',
            'vs.': 'versus',
            'Inc.': 'Incorporated',
            'Corp.': 'Corporation',
            'Ltd.': 'Limited'
        }
        
        for abbrev, full in abbreviations.items():
            text = text.replace(abbrev, full)
        
        # Fix numbers and symbols
        text = re.sub(r'&', ' and ', text)
        text = re.sub(r'%', ' percent', text)
        text = re.sub(r'\$(\d+)', r'\1 dollars', text)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove very short sentences (likely artifacts)
        sentences = text.split('.')
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        return '. '.join(sentences)
    
    def _split_text_into_chunks(self, text: str, max_chars: int = 4000) -> List[str]:
        """Split text into chunks suitable for TTS"""
        if len(text) <= max_chars:
            return [text]
        
        chunks = []
        sentences = text.split('. ')
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 2 <= max_chars:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _combine_audio_files(self, temp_files: List[Path], output_file: Path) -> None:
        """Combine multiple audio files into one (simplified)"""
        try:
            if pygame is None:
                # Fallback: just use the first file
                logger.warning("pygame not available, using first audio chunk only")
                self.file_handler.copy_file(temp_files[0], output_file)
                return
            
            # Simple concatenation using file operations
            with open(output_file, 'wb') as outfile:
                for temp_file in temp_files:
                    with open(temp_file, 'rb') as infile:
                        outfile.write(infile.read())
            
        except Exception as e:
            logger.warning(f"Error combining audio files: {e}, using first chunk")
            self.file_handler.copy_file(temp_files[0], output_file)
    
    def _create_audio_metadata(self, processed_content: ProcessedContent, audio_file: str) -> Dict[str, Any]:
        """Create metadata for the audio file"""
        return {
            'title': processed_content.metadata.get('title', 'Document'),
            'author': processed_content.metadata.get('author', ''),
            'description': processed_content.summary,
            'audio_file': audio_file,
            'duration_estimate': f"{processed_content.reading_time * 60} seconds",
            'language': 'en',  # Default language
            'keywords': processed_content.keywords,
            'difficulty': processed_content.difficulty_level,
            'sections': len(processed_content.sections),
            'generated_by': 'DocuMorph',
            'format': 'mp3'
        }
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system usage"""
        import re
        filename = filename.replace(' ', '_')
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        return filename.lower()[:50] if filename else 'audio'
