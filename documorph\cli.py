#!/usr/bin/env python3
"""
CLI Interface for DocuMorph

This module provides the command-line interface for the DocuMorph application.
"""

import sys
import click
import logging
from pathlib import Path
from typing import List, Optional
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich import print as rprint

from .core.document_parser import DocumentParser
from .core.content_processor import ContentProcessor
from .core.output_generator import OutputGenerator
from .utils.logger import setup_logger
from .utils.validators import FileValidator, InputValidator
from config.config_manager import config_manager

# Initialize console and logger
console = Console()
logger = setup_logger()


@click.group()
@click.version_option(version="1.0.0", prog_name="DocuMorph")
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--quiet', '-q', is_flag=True, help='Suppress output')
@click.pass_context
def cli(ctx, config, verbose, quiet):
    """
    DocuMorph - A comprehensive document transformation CLI tool
    
    Transform documents (PDF, DOCX, TXT, MD) into multiple output formats:
    - Responsive HTML/CSS web pages
    - Interactive quizzes
    - Audio podcasts
    - Multi-language translations
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Set logging level
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif quiet:
        logging.getLogger().setLevel(logging.ERROR)
    
    # Load custom config if provided
    if config:
        try:
            config_manager.load_user_config(config)
            logger.info(f"Loaded configuration from {config}")
        except Exception as e:
            console.print(f"[red]Error loading config: {e}[/red]")
            sys.exit(1)
    
    # Store context
    ctx.obj['config'] = config
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output-format', '-f', multiple=True, 
              type=click.Choice(['html', 'quiz', 'audio', 'translation']),
              default=['html'], help='Output format(s)')
@click.option('--output-dir', '-o', type=click.Path(), default='./output',
              help='Output directory')
@click.option('--languages', '-l', help='Target languages for translation (comma-separated)')
@click.option('--voice', help='Voice for audio generation')
@click.option('--theme', help='Theme for HTML output')
@click.option('--config-file', type=click.Path(exists=True), help='Custom configuration file')
@click.pass_context
def convert(ctx, input_file, output_format, output_dir, languages, voice, theme, config_file):
    """
    Convert a document to specified output format(s)
    
    Examples:
    \b
        documorph convert document.pdf --output-format html
        documorph convert document.docx --output-format quiz,audio
        documorph convert document.md --output-format translation --languages es,fr,de
    """
    try:
        # Validate inputs
        validator = InputValidator()
        file_validator = FileValidator()
        
        if not file_validator.validate_file(input_file):
            console.print(f"[red]Invalid input file: {input_file}[/red]")
            sys.exit(1)
        
        for fmt in output_format:
            if not validator.validate_output_format(fmt):
                console.print(f"[red]Unsupported output format: {fmt}[/red]")
                sys.exit(1)
        
        if not validator.validate_output_directory(output_dir):
            console.print(f"[red]Invalid output directory: {output_dir}[/red]")
            sys.exit(1)
        
        # Parse languages if provided
        target_languages = []
        if languages:
            target_languages = [lang.strip() for lang in languages.split(',')]
            for lang in target_languages:
                if not validator.validate_language_code(lang):
                    console.print(f"[red]Unsupported language code: {lang}[/red]")
                    sys.exit(1)
        
        # Show processing info
        if not ctx.obj.get('quiet'):
            console.print(f"[blue]Processing:[/blue] {input_file}")
            console.print(f"[blue]Output formats:[/blue] {', '.join(output_format)}")
            console.print(f"[blue]Output directory:[/blue] {output_dir}")
        
        # Initialize components
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            disable=ctx.obj.get('quiet', False)
        ) as progress:
            
            # Parse document
            parse_task = progress.add_task("Parsing document...", total=None)
            parsed_doc = parser.parse(input_file)
            progress.update(parse_task, completed=True)
            
            # Process content
            process_task = progress.add_task("Processing content...", total=None)
            processed_content = processor.process(parsed_doc)
            progress.update(process_task, completed=True)
            
            # Generate outputs
            for fmt in output_format:
                gen_task = progress.add_task(f"Generating {fmt} output...", total=None)
                
                # Set format-specific options
                options = {}
                if fmt == 'translation' and target_languages:
                    options['languages'] = target_languages
                if fmt == 'audio' and voice:
                    options['voice'] = voice
                if fmt == 'html' and theme:
                    options['theme'] = theme
                
                # Generate output
                output_path = generator.generate(
                    processed_content, 
                    fmt, 
                    output_dir,
                    **options
                )
                
                progress.update(gen_task, completed=True)
                
                if not ctx.obj.get('quiet'):
                    console.print(f"[green]✓[/green] Generated {fmt} output: {output_path}")
        
        if not ctx.obj.get('quiet'):
            console.print(f"[green]✅ Conversion completed successfully![/green]")
    
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        console.print(f"[red]❌ Conversion failed: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True))
@click.option('--output-format', '-f', multiple=True,
              type=click.Choice(['html', 'quiz', 'audio', 'translation']),
              default=['html'], help='Output format(s)')
@click.option('--output-dir', '-o', type=click.Path(), default='./batch_output',
              help='Output directory')
@click.option('--pattern', default='*', help='File pattern to match')
@click.option('--recursive', '-r', is_flag=True, help='Search recursively')
@click.pass_context
def batch_convert(ctx, input_dir, output_format, output_dir, pattern, recursive):
    """
    Convert multiple documents in batch
    
    Examples:
    \b
        documorph batch-convert ./documents --output-format html
        documorph batch-convert ./docs --pattern "*.pdf" --recursive
    """
    try:
        from .utils.file_handler import FileHandler
        
        file_handler = FileHandler()
        validator = FileValidator()
        
        # Find files to process
        files = file_handler.list_files(input_dir, pattern, recursive)
        valid_files = validator.validate_batch_files(files)
        
        if not valid_files:
            console.print(f"[yellow]No valid files found in {input_dir}[/yellow]")
            return
        
        if not ctx.obj.get('quiet'):
            console.print(f"[blue]Found {len(valid_files)} files to process[/blue]")
        
        # Initialize components
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        
        success_count = 0
        error_count = 0
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            disable=ctx.obj.get('quiet', False)
        ) as progress:
            
            main_task = progress.add_task("Processing files...", total=len(valid_files))
            
            for file_path in valid_files:
                try:
                    file_task = progress.add_task(f"Processing {Path(file_path).name}...", total=None)
                    
                    # Parse and process
                    parsed_doc = parser.parse(file_path)
                    processed_content = processor.process(parsed_doc)
                    
                    # Generate outputs
                    for fmt in output_format:
                        generator.generate(processed_content, fmt, output_dir)
                    
                    progress.update(file_task, completed=True)
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")
                    error_count += 1
                
                progress.update(main_task, advance=1)
        
        # Show results
        if not ctx.obj.get('quiet'):
            console.print(f"[green]✅ Batch conversion completed![/green]")
            console.print(f"[green]Successful:[/green] {success_count}")
            if error_count > 0:
                console.print(f"[red]Errors:[/red] {error_count}")
    
    except Exception as e:
        logger.error(f"Batch conversion failed: {e}")
        console.print(f"[red]❌ Batch conversion failed: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
def info(input_file):
    """
    Show information about a document
    
    Example:
        documorph info document.pdf
    """
    try:
        parser = DocumentParser()
        parsed_doc = parser.parse(input_file)
        
        # Create info table
        table = Table(title=f"Document Information: {Path(input_file).name}")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")
        
        # Add metadata
        for key, value in parsed_doc.metadata.items():
            if isinstance(value, (str, int, float)):
                table.add_row(key.replace('_', ' ').title(), str(value))
        
        console.print(table)
        
        # Show structure info
        structure = parsed_doc.structure
        if structure:
            console.print("\n[bold]Document Structure:[/bold]")
            
            if 'headings' in structure and structure['headings']:
                console.print(f"  Headings: {len(structure['headings'])}")
            
            if 'paragraphs' in structure and structure['paragraphs']:
                console.print(f"  Paragraphs: {len(structure['paragraphs'])}")
            
            if 'tables' in structure and structure['tables']:
                console.print(f"  Tables: {len(structure['tables'])}")
            
            if 'images' in structure and structure['images']:
                console.print(f"  Images: {len(structure['images'])}")
    
    except Exception as e:
        logger.error(f"Error getting document info: {e}")
        console.print(f"[red]❌ Error: {e}[/red]")
        sys.exit(1)


@cli.command()
def formats():
    """
    List supported input and output formats
    """
    # Input formats
    console.print("[bold]Supported Input Formats:[/bold]")
    input_formats = [
        ("PDF", ".pdf", "Portable Document Format"),
        ("DOCX", ".docx", "Microsoft Word Document"),
        ("TXT", ".txt", "Plain Text"),
        ("Markdown", ".md", "Markdown Document")
    ]
    
    input_table = Table()
    input_table.add_column("Format", style="cyan")
    input_table.add_column("Extension", style="yellow")
    input_table.add_column("Description", style="white")
    
    for name, ext, desc in input_formats:
        input_table.add_row(name, ext, desc)
    
    console.print(input_table)
    
    # Output formats
    console.print("\n[bold]Supported Output Formats:[/bold]")
    output_formats = [
        ("HTML", "Responsive web pages with CSS"),
        ("Quiz", "Interactive quizzes (JSON/HTML)"),
        ("Audio", "Audio podcasts (MP3/WAV)"),
        ("Translation", "Multi-language content")
    ]
    
    output_table = Table()
    output_table.add_column("Format", style="cyan")
    output_table.add_column("Description", style="white")
    
    for name, desc in output_formats:
        output_table.add_row(name, desc)
    
    console.print(output_table)


@cli.command()
@click.option('--show-all', is_flag=True, help='Show all configuration options')
def config(show_all):
    """
    Show current configuration
    """
    config_data = config_manager.get_all()
    
    if show_all:
        # Show full configuration
        import yaml
        console.print("[bold]Current Configuration:[/bold]")
        console.print(yaml.dump(config_data, default_flow_style=False))
    else:
        # Show key settings
        table = Table(title="Key Configuration Settings")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="white")
        
        key_settings = [
            ("Max File Size", config_data.get('processing', {}).get('max_file_size', 'N/A')),
            ("Supported Formats", ', '.join(config_data.get('processing', {}).get('supported_formats', []))),
            ("Log Level", config_data.get('logging', {}).get('level', 'N/A')),
            ("Output Quality", config_data.get('processing', {}).get('output_quality', 'N/A')),
        ]
        
        for setting, value in key_settings:
            table.add_row(setting, str(value))
        
        console.print(table)


def main():
    """Main entry point for the CLI"""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        console.print(f"[red]❌ Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    main()
