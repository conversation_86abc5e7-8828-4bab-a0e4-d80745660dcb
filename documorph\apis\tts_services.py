#!/usr/bin/env python3
"""
Text-to-Speech services integration for DocuMorph
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional

try:
    from gtts import gTTS
except ImportError:
    gTTS = None

from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class TTSService:
    """
    Text-to-Speech service integration (free services)
    """
    
    def __init__(self):
        """Initialize TTS service"""
        self.provider = config_manager.get('apis.tts_service.provider', 'gtts')
        self.rate_limit = config_manager.get('apis.tts_service.rate_limit', 50)
        self.timeout = config_manager.get('apis.tts_service.timeout', 60)
        self.chunk_size = config_manager.get('apis.tts_service.chunk_size', 1000)
        
        self.request_count = 0
        self.last_request_time = 0
        
        if self.provider == 'gtts' and gTTS is None:
            raise ImportError("gTTS is required. Install with: pip install gtts")
        
        logger.info(f"TTSService initialized with provider: {self.provider}")
    
    def text_to_speech(self, text: str, language: str = 'en', 
                      output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert text to speech
        
        Args:
            text: Text to convert
            language: Language code
            output_file: Output file path (optional)
            
        Returns:
            TTS result dictionary
        """
        try:
            if self.provider == 'gtts':
                return self._gtts_convert(text, language, output_file)
            else:
                raise ValueError(f"Unsupported TTS provider: {self.provider}")
                
        except Exception as e:
            logger.error(f"TTS conversion failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'output_file': None
            }
    
    def _gtts_convert(self, text: str, language: str, output_file: Optional[str]) -> Dict[str, Any]:
        """Convert text to speech using Google TTS"""
        try:
            self._check_rate_limit()
            
            # Create TTS object
            tts = gTTS(text=text, lang=language, slow=False)
            
            # Generate output file path if not provided
            if not output_file:
                from ..utils.file_handler import FileHandler
                file_handler = FileHandler()
                temp_file = file_handler.create_temp_file(suffix='.mp3', prefix='tts_')
                output_file = str(temp_file)
            
            # Save audio file
            tts.save(output_file)
            
            self.request_count += 1
            self.last_request_time = time.time()
            
            return {
                'success': True,
                'output_file': output_file,
                'language': language,
                'provider': 'gtts',
                'text_length': len(text)
            }
            
        except Exception as e:
            logger.error(f"gTTS conversion failed: {e}")
            raise
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get supported languages for TTS"""
        if self.provider == 'gtts':
            return {
                'en': 'English',
                'es': 'Spanish',
                'fr': 'French',
                'de': 'German',
                'it': 'Italian',
                'pt': 'Portuguese',
                'ru': 'Russian',
                'ja': 'Japanese',
                'ko': 'Korean',
                'zh': 'Chinese',
                'ar': 'Arabic',
                'hi': 'Hindi',
                'tr': 'Turkish',
                'pl': 'Polish',
                'nl': 'Dutch',
                'sv': 'Swedish',
                'da': 'Danish',
                'no': 'Norwegian',
                'fi': 'Finnish'
            }
        return {}
    
    def _check_rate_limit(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Reset counter every minute
        if current_time - self.last_request_time > 60:
            self.request_count = 0
        
        # Check if we've exceeded rate limit
        if self.request_count >= self.rate_limit:
            sleep_time = 60 - (current_time - self.last_request_time)
            if sleep_time > 0:
                logger.info(f"TTS rate limit reached, sleeping for {sleep_time:.1f} seconds")
                time.sleep(sleep_time)
                self.request_count = 0
