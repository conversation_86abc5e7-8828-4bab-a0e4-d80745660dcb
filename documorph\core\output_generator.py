#!/usr/bin/env python3
"""
Output Generator for DocuMorph

This module coordinates the generation of different output formats.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional

from .content_processor import ProcessedContent
from ..generators.web_generator import WebGenerator
from ..generators.quiz_generator import QuizGenerator
from ..generators.audio_generator import AudioGenerator
from ..generators.translation_generator import TranslationGenerator
from ..utils.file_handler import FileHandler
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class OutputGenerator:
    """
    Main output generator that coordinates different format generators
    """
    
    def __init__(self):
        """Initialize the output generator"""
        self.file_handler = FileHandler()
        
        # Initialize format-specific generators
        self.generators = {
            'html': WebGenerator(),
            'quiz': QuizGenerator(),
            'audio': AudioGenerator(),
            'translation': TranslationGenerator()
        }
        
        logger.info("OutputGenerator initialized with generators: %s", 
                   list(self.generators.keys()))
    
    def generate(self, processed_content: ProcessedContent, 
                output_format: str, output_dir: str, **kwargs) -> str:
        """
        Generate output in specified format
        
        Args:
            processed_content: ProcessedContent object
            output_format: Output format ('html', 'quiz', 'audio', 'translation')
            output_dir: Output directory path
            **kwargs: Format-specific options
            
        Returns:
            Path to generated output
            
        Raises:
            ValueError: If output format is not supported
            Exception: For generation errors
        """
        try:
            if output_format not in self.generators:
                raise ValueError(f"Unsupported output format: {output_format}")
            
            logger.info(f"Generating {output_format} output to {output_dir}")
            
            # Ensure output directory exists
            output_path = self.file_handler.ensure_output_directory(output_dir)
            
            # Get appropriate generator
            generator = self.generators[output_format]
            
            # Generate output
            result_path = generator.generate(processed_content, str(output_path), **kwargs)
            
            logger.info(f"Successfully generated {output_format} output: {result_path}")
            return result_path
            
        except Exception as e:
            logger.error(f"Error generating {output_format} output: {e}")
            raise
    
    def generate_multiple(self, processed_content: ProcessedContent,
                         output_formats: list, output_dir: str, **kwargs) -> Dict[str, str]:
        """
        Generate multiple output formats
        
        Args:
            processed_content: ProcessedContent object
            output_formats: List of output formats
            output_dir: Output directory path
            **kwargs: Format-specific options
            
        Returns:
            Dictionary mapping format to output path
        """
        results = {}
        errors = {}
        
        for output_format in output_formats:
            try:
                result_path = self.generate(processed_content, output_format, output_dir, **kwargs)
                results[output_format] = result_path
            except Exception as e:
                errors[output_format] = str(e)
                logger.error(f"Failed to generate {output_format}: {e}")
        
        if errors:
            logger.warning(f"Generation completed with errors: {errors}")
        
        return results
    
    def get_supported_formats(self) -> list:
        """
        Get list of supported output formats
        
        Returns:
            List of supported format strings
        """
        return list(self.generators.keys())
    
    def get_generator_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about available generators
        
        Returns:
            Dictionary with generator information
        """
        info = {}
        for format_name, generator in self.generators.items():
            info[format_name] = {
                'class': generator.__class__.__name__,
                'description': getattr(generator, 'description', 'No description available'),
                'supported_options': getattr(generator, 'supported_options', [])
            }
        return info
