#!/usr/bin/env python3
"""
Google Translate API integration for DocuMorph
"""

import logging
import time
from typing import Dict, Any, Optional, List

try:
    from googletrans import Translator
    import requests
except ImportError:
    Translator = requests = None

from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class GoogleTranslateAPI:
    """
    Google Translate API integration (free tier)
    """
    
    def __init__(self):
        """Initialize Google Translate API"""
        if Translator is None:
            raise ImportError("googletrans is required. Install with: pip install googletrans==4.0.0-rc1")
        
        self.translator = Translator()
        self.rate_limit = config_manager.get('apis.google_translate.rate_limit', 100)
        self.timeout = config_manager.get('apis.google_translate.timeout', 30)
        self.retry_attempts = config_manager.get('apis.google_translate.retry_attempts', 3)
        
        self.request_count = 0
        self.last_request_time = 0
        
        logger.info("GoogleTranslateAPI initialized")
    
    def translate(self, text: str, target_lang: str, source_lang: str = 'auto') -> Dict[str, Any]:
        """
        Translate text to target language
        
        Args:
            text: Text to translate
            target_lang: Target language code
            source_lang: Source language code (default: auto-detect)
            
        Returns:
            Translation result dictionary
        """
        try:
            self._check_rate_limit()
            
            for attempt in range(self.retry_attempts):
                try:
                    result = self.translator.translate(
                        text, 
                        dest=target_lang, 
                        src=source_lang
                    )
                    
                    self.request_count += 1
                    self.last_request_time = time.time()
                    
                    return {
                        'translated_text': result.text,
                        'source_language': result.src,
                        'target_language': target_lang,
                        'confidence': getattr(result, 'confidence', None),
                        'success': True
                    }
                    
                except Exception as e:
                    logger.warning(f"Translation attempt {attempt + 1} failed: {e}")
                    if attempt < self.retry_attempts - 1:
                        time.sleep(1)  # Wait before retry
                    else:
                        raise
            
        except Exception as e:
            logger.error(f"Translation failed: {e}")
            return {
                'translated_text': text,  # Return original on error
                'source_language': source_lang,
                'target_language': target_lang,
                'error': str(e),
                'success': False
            }
    
    def detect_language(self, text: str) -> Dict[str, Any]:
        """
        Detect language of text
        
        Args:
            text: Text to analyze
            
        Returns:
            Language detection result
        """
        try:
            self._check_rate_limit()
            
            result = self.translator.detect(text)
            
            self.request_count += 1
            self.last_request_time = time.time()
            
            return {
                'language': result.lang,
                'confidence': result.confidence,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            return {
                'language': 'en',  # Default to English
                'confidence': 0.0,
                'error': str(e),
                'success': False
            }
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        # Common language codes supported by Google Translate
        return [
            'af', 'sq', 'am', 'ar', 'hy', 'az', 'eu', 'be', 'bn', 'bs', 'bg', 'ca',
            'ceb', 'ny', 'zh', 'co', 'hr', 'cs', 'da', 'nl', 'en', 'eo', 'et', 'tl',
            'fi', 'fr', 'fy', 'gl', 'ka', 'de', 'el', 'gu', 'ht', 'ha', 'haw', 'iw',
            'hi', 'hmn', 'hu', 'is', 'ig', 'id', 'ga', 'it', 'ja', 'jw', 'kn', 'kk',
            'km', 'ko', 'ku', 'ky', 'lo', 'la', 'lv', 'lt', 'lb', 'mk', 'mg', 'ms',
            'ml', 'mt', 'mi', 'mr', 'mn', 'my', 'ne', 'no', 'ps', 'fa', 'pl', 'pt',
            'pa', 'ro', 'ru', 'sm', 'gd', 'sr', 'st', 'sn', 'sd', 'si', 'sk', 'sl',
            'so', 'es', 'su', 'sw', 'sv', 'tg', 'ta', 'te', 'th', 'tr', 'uk', 'ur',
            'uz', 'vi', 'cy', 'xh', 'yi', 'yo', 'zu'
        ]
    
    def _check_rate_limit(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Reset counter every minute
        if current_time - self.last_request_time > 60:
            self.request_count = 0
        
        # Check if we've exceeded rate limit
        if self.request_count >= self.rate_limit:
            sleep_time = 60 - (current_time - self.last_request_time)
            if sleep_time > 0:
                logger.info(f"Rate limit reached, sleeping for {sleep_time:.1f} seconds")
                time.sleep(sleep_time)
                self.request_count = 0
