# API Integration Guide

DocuMorph integrates with several free APIs to provide translation and text-to-speech functionality. This guide covers setup, configuration, and usage of these integrations.

## Overview

DocuMorph uses the following free APIs:

| Service | Purpose | Provider | Cost |
|---------|---------|----------|------|
| Google Translate | Text translation | Google | Free tier available |
| gTTS | Text-to-speech | Google | Free |
| googletrans | Translation library | Community | Free |

## Google Translate Integration

### Free Tier (googletrans)

DocuMorph uses the `googletrans` library by default, which provides free access to Google Translate.

#### Setup
No API key required - works out of the box:

```python
from documorph.apis.google_translate import GoogleTranslateAPI

translator = GoogleTranslateAPI()
result = translator.translate("Hello world", "es")
print(result['translated_text'])  # "Hola mundo"
```

#### Rate Limits
- **Requests**: ~100 requests per minute
- **Characters**: ~5000 characters per request
- **Daily limit**: Varies based on usage patterns

#### Supported Languages
The free tier supports 100+ languages including:
- Spanish (es), French (fr), German (de)
- Italian (it), Portuguese (pt), Russian (ru)
- Japanese (ja), Korean (ko), Chinese (zh)
- Arabic (ar), Hindi (hi), Turkish (tr)

### Official Google Translate API (Optional)

For higher limits and guaranteed availability, you can use the official API:

#### Setup
1. Create a Google Cloud Project
2. Enable the Translation API
3. Create an API key
4. Set environment variable:

```bash
export GOOGLE_TRANSLATE_API_KEY="your_api_key_here"
```

#### Configuration
```yaml
# config.yaml
apis:
  google_translate:
    api_key: "your_api_key"
    rate_limit: 1000
    timeout: 30
    retry_attempts: 3
```

#### Usage Limits
- **Free tier**: $300 credit for new accounts
- **Pricing**: $20 per million characters
- **Rate limits**: 100 requests per 100 seconds per user

## Text-to-Speech Integration

### gTTS (Google Text-to-Speech)

DocuMorph uses gTTS for free text-to-speech conversion.

#### Setup
Included with DocuMorph installation:

```bash
pip install gtts
```

#### Configuration
```yaml
# config.yaml
apis:
  tts_service:
    provider: "gtts"
    rate_limit: 50
    timeout: 60
    chunk_size: 1000
```

#### Supported Languages
gTTS supports 50+ languages with various accents:

```python
# Available languages
languages = {
    'en': 'English',
    'es': 'Spanish', 
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese'
}
```

#### Usage Example
```python
from documorph.apis.tts_services import TTSService

tts = TTSService()
result = tts.text_to_speech(
    text="Hello, this is a test.",
    language="en",
    output_file="output.mp3"
)
```

#### Rate Limits
- **Requests**: ~50 requests per minute
- **Text length**: ~1000 characters per request
- **File size**: Generated MP3 files are typically 1-2MB per minute of audio

## Error Handling and Retry Logic

### Automatic Retries

DocuMorph implements automatic retry logic for API failures:

```python
# Retry configuration
retry_attempts = 3
timeout = 30
backoff_factor = 1.5
```

### Rate Limiting

Built-in rate limiting prevents API quota exhaustion:

```python
class RateLimiter:
    def __init__(self, max_requests=100, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def check_rate_limit(self):
        # Implementation handles rate limiting
        pass
```

### Error Types and Handling

| Error Type | Cause | Handling |
|------------|-------|----------|
| `ConnectionError` | Network issues | Retry with backoff |
| `TimeoutError` | Request timeout | Retry with longer timeout |
| `QuotaExceededError` | Rate limit hit | Wait and retry |
| `AuthenticationError` | Invalid API key | Fail with clear message |
| `ServiceUnavailableError` | API down | Retry with exponential backoff |

## Configuration Examples

### Basic Configuration
```yaml
# Minimal configuration for free services
apis:
  google_translate:
    rate_limit: 100
  tts_service:
    provider: "gtts"
    rate_limit: 50
```

### Advanced Configuration
```yaml
# Advanced configuration with all options
apis:
  google_translate:
    api_key: "${GOOGLE_TRANSLATE_API_KEY}"
    rate_limit: 1000
    timeout: 30
    retry_attempts: 3
    backoff_factor: 1.5
    
  tts_service:
    provider: "gtts"
    rate_limit: 100
    timeout: 60
    chunk_size: 1500
    quality: "high"
    
  # Fallback configuration
  fallback:
    translation_enabled: true
    tts_enabled: true
    offline_mode: false
```

### Environment Variables
```bash
# API Configuration
export GOOGLE_TRANSLATE_API_KEY="your_api_key"
export DOCUMORPH_TTS_PROVIDER="gtts"
export DOCUMORPH_TRANSLATION_RATE_LIMIT="100"
export DOCUMORPH_TTS_RATE_LIMIT="50"

# Proxy configuration (if needed)
export HTTP_PROXY="http://proxy.company.com:8080"
export HTTPS_PROXY="https://proxy.company.com:8080"
```

## Usage in Code

### Translation Example
```python
from documorph.core.document_parser import DocumentParser
from documorph.core.content_processor import ContentProcessor
from documorph.core.output_generator import OutputGenerator

# Parse document
parser = DocumentParser()
parsed_doc = parser.parse("document.pdf")

# Process content
processor = ContentProcessor()
processed_content = processor.process(parsed_doc)

# Generate translations
generator = OutputGenerator()
translation_output = generator.generate(
    processed_content,
    'translation',
    './output',
    languages=['es', 'fr', 'de']
)
```

### Audio Generation Example
```python
# Generate audio with custom voice
audio_output = generator.generate(
    processed_content,
    'audio',
    './output',
    voice='es',  # Spanish voice
    add_intro=True,
    chapter_breaks=True
)
```

## Monitoring and Logging

### API Usage Tracking
```python
# Enable API usage logging
import logging
logging.getLogger('documorph.apis').setLevel(logging.INFO)
```

### Metrics Collection
DocuMorph tracks API usage metrics:

```python
# Example metrics
metrics = {
    'translation_requests': 150,
    'translation_characters': 45000,
    'tts_requests': 25,
    'tts_characters': 12000,
    'errors': 2,
    'rate_limit_hits': 1
}
```

## Troubleshooting

### Common Issues

#### 1. Translation Not Working
```bash
# Check internet connection
ping translate.googleapis.com

# Test with simple example
documorph convert test.txt -f translation -l es
```

#### 2. TTS Generation Fails
```bash
# Verify gTTS installation
python -c "import gtts; print('gTTS working')"

# Test with short text
documorph convert short.txt -f audio
```

#### 3. Rate Limit Errors
```bash
# Reduce rate limits in config
apis:
  google_translate:
    rate_limit: 50  # Reduced from 100
  tts_service:
    rate_limit: 25  # Reduced from 50
```

#### 4. Proxy Issues
```bash
# Configure proxy settings
export HTTP_PROXY="http://proxy:8080"
export HTTPS_PROXY="https://proxy:8080"
export NO_PROXY="localhost,127.0.0.1"
```

### Debug Mode
Enable debug logging for API calls:

```bash
# Run with debug logging
DOCUMORPH_LOG_LEVEL=DEBUG documorph convert doc.pdf -f translation -l es
```

## Best Practices

### 1. Rate Limiting
- Use conservative rate limits to avoid quota exhaustion
- Implement exponential backoff for retries
- Monitor API usage patterns

### 2. Error Handling
- Always handle API failures gracefully
- Provide fallback options when possible
- Log errors for debugging

### 3. Content Optimization
- Split large texts into smaller chunks
- Remove unnecessary formatting before translation
- Use appropriate languages for target audiences

### 4. Caching
- Cache translation results to reduce API calls
- Store audio files to avoid regeneration
- Implement cache invalidation strategies

## API Alternatives

If the default APIs don't meet your needs, consider these alternatives:

### Translation Alternatives
- **Microsoft Translator**: Free tier available
- **DeepL API**: High-quality translations
- **LibreTranslate**: Open-source, self-hosted

### TTS Alternatives
- **Amazon Polly**: High-quality voices
- **Microsoft Speech**: Neural voices
- **eSpeak**: Offline, open-source

## Support and Resources

- **API Documentation**: Links to official API docs
- **Community Forum**: Discussion and troubleshooting
- **GitHub Issues**: Bug reports and feature requests
- **Stack Overflow**: Technical questions and answers

For additional help with API integration, visit our [GitHub repository](https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas) or contact the development team.
