#!/usr/bin/env python3
"""
Basic usage examples for DocuMorph
"""

from pathlib import Path
from documorph.core.document_parser import DocumentParser
from documorph.core.content_processor import ContentProcessor
from documorph.core.output_generator import OutputGenerator


def basic_conversion_example():
    """Basic document conversion example"""
    print("=== Basic Document Conversion ===")
    
    # Initialize components
    parser = DocumentParser()
    processor = ContentProcessor()
    generator = OutputGenerator()
    
    # Example with a text file (you would replace with actual file)
    sample_text = """# Sample Document

This is a sample document for testing DocuMorph.

## Introduction

DocuMorph is a comprehensive document transformation tool that can convert documents into multiple formats.

## Features

- PDF parsing
- DOCX support
- Markdown processing
- HTML generation
- Quiz creation
- Audio conversion
- Multi-language translation

## Conclusion

This tool makes document transformation easy and efficient.
"""
    
    # Create a temporary markdown file
    temp_file = Path("temp_sample.md")
    with open(temp_file, 'w', encoding='utf-8') as f:
        f.write(sample_text)
    
    try:
        # Parse document
        print("1. Parsing document...")
        parsed_doc = parser.parse(str(temp_file))
        print(f"   - Format: {parsed_doc.format}")
        print(f"   - Content length: {len(parsed_doc.content)} characters")
        
        # Process content
        print("2. Processing content...")
        processed_content = processor.process(parsed_doc)
        print(f"   - Sections: {len(processed_content.sections)}")
        print(f"   - Reading time: {processed_content.reading_time} minutes")
        print(f"   - Difficulty: {processed_content.difficulty_level}")
        
        # Generate HTML output
        print("3. Generating HTML output...")
        html_output = generator.generate(processed_content, 'html', './output')
        print(f"   - Generated: {html_output}")
        
        # Generate quiz
        print("4. Generating quiz...")
        quiz_output = generator.generate(processed_content, 'quiz', './output')
        print(f"   - Generated: {quiz_output}")
        
        print("\n✅ Basic conversion completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # Clean up
        if temp_file.exists():
            temp_file.unlink()


def batch_processing_example():
    """Batch processing example"""
    print("\n=== Batch Processing Example ===")
    
    # Create sample files
    sample_files = []
    sample_contents = [
        ("Document 1", "# First Document\n\nThis is the first sample document."),
        ("Document 2", "# Second Document\n\nThis is the second sample document."),
        ("Document 3", "# Third Document\n\nThis is the third sample document.")
    ]
    
    try:
        # Create temporary files
        for title, content in sample_contents:
            filename = f"temp_{title.lower().replace(' ', '_')}.md"
            filepath = Path(filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            sample_files.append(filepath)
        
        # Initialize components
        parser = DocumentParser()
        processor = ContentProcessor()
        generator = OutputGenerator()
        
        print(f"Processing {len(sample_files)} files...")
        
        # Process each file
        for i, file_path in enumerate(sample_files, 1):
            print(f"\n{i}. Processing {file_path.name}...")
            
            # Parse and process
            parsed_doc = parser.parse(str(file_path))
            processed_content = processor.process(parsed_doc)
            
            # Generate multiple outputs
            outputs = generator.generate_multiple(
                processed_content,
                ['html', 'quiz'],
                './batch_output'
            )
            
            for format_type, output_path in outputs.items():
                print(f"   - {format_type}: {output_path}")
        
        print("\n✅ Batch processing completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # Clean up
        for file_path in sample_files:
            if file_path.exists():
                file_path.unlink()


def configuration_example():
    """Configuration example"""
    print("\n=== Configuration Example ===")
    
    from config.config_manager import config_manager
    
    # Show current configuration
    print("Current configuration:")
    print(f"- Max file size: {config_manager.get('processing.max_file_size')}")
    print(f"- Supported formats: {config_manager.get('processing.supported_formats')}")
    print(f"- HTML theme: {config_manager.get('output.html.theme')}")
    print(f"- Quiz question types: {config_manager.get('output.quiz.question_types')}")
    
    # Example of updating configuration
    print("\nUpdating configuration...")
    config_manager.set('output.html.theme', 'dark')
    config_manager.set('output.quiz.questions_per_section', 5)
    
    print("Updated configuration:")
    print(f"- HTML theme: {config_manager.get('output.html.theme')}")
    print(f"- Questions per section: {config_manager.get('output.quiz.questions_per_section')}")


if __name__ == '__main__':
    print("DocuMorph Usage Examples")
    print("=" * 50)
    
    # Run examples
    basic_conversion_example()
    batch_processing_example()
    configuration_example()
    
    print("\n" + "=" * 50)
    print("Examples completed! Check the output directories for generated files.")
    print("\nTo run DocuMorph from command line:")
    print("  documorph convert document.pdf --output-format html")
    print("  documorph convert document.docx --output-format quiz,audio")
    print("  documorph batch-convert ./documents --output-format html")
