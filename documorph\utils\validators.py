#!/usr/bin/env python3
"""
Validators for DocuMorph

This module provides validation utilities for files, inputs, and configurations.
"""

import os
import re
import logging
from pathlib import Path
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


class FileValidator:
    """
    File validation utilities
    """
    
    def __init__(self):
        """Initialize the file validator"""
        self.allowed_extensions = ['.pdf', '.docx', '.doc', '.txt', '.md', '.markdown']
        self.max_filename_length = 255
        self.dangerous_patterns = [
            r'\.\./',  # Directory traversal
            r'[<>:"|?*]',  # Invalid filename characters on Windows
            r'^\s*$',  # Empty or whitespace-only names
        ]
    
    def validate_file(self, file_path: Union[str, Path], max_size: str = "50MB") -> bool:
        """
        Validate a file for processing
        
        Args:
            file_path: Path to the file
            max_size: Maximum file size (e.g., "50MB", "1GB")
            
        Returns:
            True if file is valid, False otherwise
        """
        try:
            file_path = Path(file_path)
            
            # Check if file exists
            if not file_path.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
            
            # Check if it's a file (not directory)
            if not file_path.is_file():
                logger.error(f"Path is not a file: {file_path}")
                return False
            
            # Check file extension
            if not self.validate_extension(file_path):
                logger.error(f"Unsupported file extension: {file_path.suffix}")
                return False
            
            # Check file size
            if not self.validate_file_size(file_path, max_size):
                logger.error(f"File size exceeds limit: {file_path}")
                return False
            
            # Check filename
            if not self.validate_filename(file_path.name):
                logger.error(f"Invalid filename: {file_path.name}")
                return False
            
            # Check file permissions
            if not self.validate_permissions(file_path):
                logger.error(f"Insufficient permissions: {file_path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {e}")
            return False
    
    def validate_extension(self, file_path: Union[str, Path]) -> bool:
        """
        Validate file extension
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if extension is allowed
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        return extension in self.allowed_extensions
    
    def validate_file_size(self, file_path: Union[str, Path], max_size: str) -> bool:
        """
        Validate file size
        
        Args:
            file_path: Path to the file
            max_size: Maximum size string (e.g., "50MB")
            
        Returns:
            True if file size is within limit
        """
        try:
            file_path = Path(file_path)
            file_size = file_path.stat().st_size
            max_bytes = self._parse_size_string(max_size)
            
            return file_size <= max_bytes
            
        except Exception as e:
            logger.error(f"Error checking file size: {e}")
            return False
    
    def validate_filename(self, filename: str) -> bool:
        """
        Validate filename for security and compatibility
        
        Args:
            filename: Name of the file
            
        Returns:
            True if filename is valid
        """
        # Check length
        if len(filename) > self.max_filename_length:
            return False
        
        # Check for dangerous patterns
        for pattern in self.dangerous_patterns:
            if re.search(pattern, filename):
                return False
        
        # Check for reserved names (Windows)
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        
        name_without_ext = Path(filename).stem.upper()
        if name_without_ext in reserved_names:
            return False
        
        return True
    
    def validate_permissions(self, file_path: Union[str, Path]) -> bool:
        """
        Validate file permissions
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is readable
        """
        try:
            file_path = Path(file_path)
            return os.access(file_path, os.R_OK)
        except Exception:
            return False
    
    def validate_batch_files(self, file_paths: List[Union[str, Path]], 
                           max_size: str = "50MB") -> List[str]:
        """
        Validate multiple files and return list of valid ones
        
        Args:
            file_paths: List of file paths
            max_size: Maximum file size
            
        Returns:
            List of valid file paths
        """
        valid_files = []
        
        for file_path in file_paths:
            if self.validate_file(file_path, max_size):
                valid_files.append(str(file_path))
            else:
                logger.warning(f"Skipping invalid file: {file_path}")
        
        return valid_files
    
    def _parse_size_string(self, size_str: str) -> int:
        """
        Parse size string to bytes
        
        Args:
            size_str: Size string (e.g., "50MB", "1GB")
            
        Returns:
            Size in bytes
        """
        size_str = size_str.upper().strip()
        
        # Extract number and unit
        match = re.match(r'^(\d+(?:\.\d+)?)\s*([KMGT]?B?)$', size_str)
        if not match:
            raise ValueError(f"Invalid size format: {size_str}")
        
        number = float(match.group(1))
        unit = match.group(2) or 'B'
        
        # Convert to bytes
        multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 ** 2,
            'GB': 1024 ** 3,
            'TB': 1024 ** 4
        }
        
        if unit not in multipliers:
            raise ValueError(f"Unknown size unit: {unit}")
        
        return int(number * multipliers[unit])


class InputValidator:
    """
    Input validation utilities
    """
    
    def __init__(self):
        """Initialize the input validator"""
        self.supported_formats = ['html', 'quiz', 'audio', 'translation']
        self.supported_languages = [
            'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
            'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi'
        ]
    
    def validate_output_format(self, format_str: str) -> bool:
        """
        Validate output format
        
        Args:
            format_str: Output format string
            
        Returns:
            True if format is supported
        """
        return format_str.lower() in self.supported_formats
    
    def validate_language_code(self, lang_code: str) -> bool:
        """
        Validate language code
        
        Args:
            lang_code: Language code (e.g., 'en', 'es')
            
        Returns:
            True if language is supported
        """
        return lang_code.lower() in self.supported_languages
    
    def validate_output_directory(self, output_dir: str) -> bool:
        """
        Validate output directory
        
        Args:
            output_dir: Output directory path
            
        Returns:
            True if directory is valid/creatable
        """
        try:
            output_path = Path(output_dir)
            
            # Check if directory exists
            if output_path.exists():
                return output_path.is_dir() and os.access(output_path, os.W_OK)
            
            # Check if parent directory exists and is writable
            parent = output_path.parent
            return parent.exists() and parent.is_dir() and os.access(parent, os.W_OK)
            
        except Exception as e:
            logger.error(f"Error validating output directory: {e}")
            return False
    
    def validate_config_file(self, config_path: str) -> bool:
        """
        Validate configuration file
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            True if config file is valid
        """
        try:
            config_path = Path(config_path)
            
            # Check if file exists and is readable
            if not config_path.exists() or not config_path.is_file():
                return False
            
            # Check extension
            if config_path.suffix.lower() not in ['.yaml', '.yml', '.json']:
                return False
            
            # Check permissions
            return os.access(config_path, os.R_OK)
            
        except Exception:
            return False


class ContentValidator:
    """
    Content validation utilities
    """
    
    def __init__(self):
        """Initialize the content validator"""
        self.min_content_length = 10
        self.max_content_length = 10_000_000  # 10MB of text
    
    def validate_content_length(self, content: str) -> bool:
        """
        Validate content length
        
        Args:
            content: Text content
            
        Returns:
            True if content length is acceptable
        """
        content_length = len(content)
        return self.min_content_length <= content_length <= self.max_content_length
    
    def validate_encoding(self, content: str) -> bool:
        """
        Validate content encoding
        
        Args:
            content: Text content
            
        Returns:
            True if content has valid encoding
        """
        try:
            # Try to encode/decode to check for encoding issues
            content.encode('utf-8').decode('utf-8')
            return True
        except UnicodeError:
            return False
    
    def sanitize_content(self, content: str) -> str:
        """
        Sanitize content for safe processing
        
        Args:
            content: Raw content
            
        Returns:
            Sanitized content
        """
        # Remove null bytes
        content = content.replace('\x00', '')
        
        # Normalize line endings
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # Remove excessive whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        return content.strip()
    
    def detect_content_type(self, content: str) -> str:
        """
        Detect content type from content
        
        Args:
            content: Text content
            
        Returns:
            Detected content type
        """
        # Simple heuristics for content type detection
        if re.search(r'<[^>]+>', content):
            return 'html'
        elif re.search(r'#{1,6}\s+', content):
            return 'markdown'
        elif re.search(r'def\s+\w+\s*\(|class\s+\w+\s*[{:]', content):
            return 'code'
        else:
            return 'text'
