#!/usr/bin/env python3
"""
DocuMorph - A comprehensive document transformation CLI tool
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="documorph",
    version="1.0.0",
    author="HectorTa1989",
    author_email="<EMAIL>",
    description="A comprehensive document transformation CLI tool",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas",
    project_urls={
        "Bug Tracker": "https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas/issues",
        "Documentation": "https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas/docs",
        "Source Code": "https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Text Processing",
        "Topic :: Utilities",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.1.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.8.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=0.971",
        ],
    },
    entry_points={
        "console_scripts": [
            "documorph=documorph.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "documorph": [
            "templates/**/*",
            "config/*.yaml",
        ],
    },
    zip_safe=False,
    keywords="document transformation cli pdf docx html quiz audio translation",
)
