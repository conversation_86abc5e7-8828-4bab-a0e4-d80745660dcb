#!/usr/bin/env python3
"""
Quiz Generator for DocuMorph

This module generates interactive quizzes from processed content.
"""

import logging
import json
import random
from pathlib import Path
from typing import Dict, Any, List

from ..core.content_processor import ProcessedContent
from ..utils.file_handler import <PERSON>Handler
from config.config_manager import config_manager

logger = logging.getLogger(__name__)


class QuizGenerator:
    """
    Generator for interactive quizzes
    """
    
    description = "Generates interactive quizzes in JSON and HTML format"
    supported_options = ['question_types', 'difficulty_levels', 'questions_per_section', 'randomize']
    
    def __init__(self):
        """Initialize the quiz generator"""
        self.file_handler = FileHandler()
        self.question_types = config_manager.get('output.quiz.question_types', 
                                               ['multiple_choice', 'true_false', 'short_answer'])
        logger.info("QuizGenerator initialized")
    
    def generate(self, processed_content: ProcessedContent, output_dir: str, **kwargs) -> str:
        """
        Generate interactive quiz
        
        Args:
            processed_content: ProcessedContent object
            output_dir: Output directory path
            **kwargs: Generation options
        
        Returns:
            Path to generated quiz file
        """
        try:
            # Generate questions
            questions = self._generate_questions(processed_content, **kwargs)
            
            # Create quiz data
            quiz_data = {
                'title': f"Quiz: {processed_content.metadata.get('title', 'Document')}",
                'description': processed_content.summary,
                'questions': questions,
                'metadata': {
                    'generated_from': processed_content.metadata.get('file_name', ''),
                    'total_questions': len(questions),
                    'difficulty': processed_content.difficulty_level,
                    'estimated_time': max(5, len(questions) * 2)  # 2 minutes per question
                }
            }
            
            # Save JSON
            title = processed_content.metadata.get('title', 'quiz')
            safe_title = self._sanitize_filename(title)
            
            output_path = Path(output_dir)
            json_file = output_path / f"{safe_title}_quiz.json"
            html_file = output_path / f"{safe_title}_quiz.html"
            
            # Save quiz data as JSON
            self.file_handler.save_json(json_file, quiz_data)
            
            # Generate HTML interface
            html_content = self._generate_quiz_html(quiz_data)
            self.file_handler.write_file(html_file, html_content)
            
            logger.info(f"Generated quiz with {len(questions)} questions: {html_file}")
            return str(html_file)
            
        except Exception as e:
            logger.error(f"Error generating quiz: {e}")
            raise
    
    def _generate_questions(self, processed_content: ProcessedContent, **kwargs) -> List[Dict[str, Any]]:
        """Generate quiz questions from content"""
        questions = []
        sections = processed_content.sections
        
        questions_per_section = kwargs.get('questions_per_section', 
                                         config_manager.get('output.quiz.questions_per_section', 3))
        
        for section in sections:
            if len(section['content']) < 100:  # Skip short sections
                continue
            
            section_questions = self._generate_section_questions(section, questions_per_section)
            questions.extend(section_questions)
        
        # Add general questions from summary and keywords
        general_questions = self._generate_general_questions(processed_content)
        questions.extend(general_questions)
        
        # Randomize if requested
        if kwargs.get('randomize', config_manager.get('output.quiz.randomize_questions', True)):
            random.shuffle(questions)
        
        return questions[:20]  # Limit to 20 questions
    
    def _generate_section_questions(self, section: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """Generate questions from a section"""
        questions = []
        content = section['content']
        title = section['title']
        
        # Simple question generation based on content patterns
        sentences = [s.strip() for s in content.split('.') if len(s.strip()) > 20]
        
        for i in range(min(count, len(sentences))):
            if i < len(sentences):
                sentence = sentences[i]
                
                # Generate multiple choice question
                if 'multiple_choice' in self.question_types:
                    mc_question = self._create_multiple_choice(sentence, title)
                    if mc_question:
                        questions.append(mc_question)
                
                # Generate true/false question
                if 'true_false' in self.question_types and len(questions) < count:
                    tf_question = self._create_true_false(sentence, title)
                    if tf_question:
                        questions.append(tf_question)
        
        return questions
    
    def _create_multiple_choice(self, sentence: str, section_title: str) -> Dict[str, Any]:
        """Create a multiple choice question"""
        words = sentence.split()
        if len(words) < 5:
            return None
        
        # Find a key term to ask about
        key_words = [w for w in words if len(w) > 4 and w.isalpha()]
        if not key_words:
            return None
        
        key_word = random.choice(key_words)
        question_text = sentence.replace(key_word, "______")
        
        # Generate options (simplified)
        options = [
            key_word,  # Correct answer
            f"not {key_word}",
            f"{key_word}s",
            "none of the above"
        ]
        random.shuffle(options)
        
        return {
            'type': 'multiple_choice',
            'question': f"Fill in the blank: {question_text}",
            'options': options,
            'correct_answer': key_word,
            'section': section_title,
            'difficulty': 'medium'
        }
    
    def _create_true_false(self, sentence: str, section_title: str) -> Dict[str, Any]:
        """Create a true/false question"""
        if len(sentence) < 20:
            return None
        
        # Create a statement that might be true or false
        is_true = random.choice([True, False])
        
        if not is_true:
            # Modify the sentence to make it false (simplified)
            words = sentence.split()
            if len(words) > 3:
                # Replace a word with "not"
                modified_words = words[:len(words)//2] + ["not"] + words[len(words)//2:]
                sentence = " ".join(modified_words)
        
        return {
            'type': 'true_false',
            'question': sentence,
            'correct_answer': is_true,
            'section': section_title,
            'difficulty': 'easy'
        }
    
    def _generate_general_questions(self, processed_content: ProcessedContent) -> List[Dict[str, Any]]:
        """Generate general questions about the document"""
        questions = []
        
        # Question about reading time
        questions.append({
            'type': 'multiple_choice',
            'question': f"What is the estimated reading time for this document?",
            'options': [
                f"{processed_content.reading_time} minutes",
                f"{processed_content.reading_time + 5} minutes", 
                f"{processed_content.reading_time - 2} minutes",
                f"{processed_content.reading_time * 2} minutes"
            ],
            'correct_answer': f"{processed_content.reading_time} minutes",
            'section': 'General',
            'difficulty': 'easy'
        })
        
        # Question about difficulty
        questions.append({
            'type': 'multiple_choice',
            'question': "What is the difficulty level of this document?",
            'options': ['easy', 'medium', 'hard', 'expert'],
            'correct_answer': processed_content.difficulty_level,
            'section': 'General',
            'difficulty': 'easy'
        })
        
        # Questions about keywords
        if processed_content.keywords:
            keyword = random.choice(processed_content.keywords)
            questions.append({
                'type': 'true_false',
                'question': f"'{keyword}' is one of the key topics in this document.",
                'correct_answer': True,
                'section': 'General',
                'difficulty': 'medium'
            })
        
        return questions
    
    def _generate_quiz_html(self, quiz_data: Dict[str, Any]) -> str:
        """Generate HTML interface for the quiz"""
        title = quiz_data['title']
        description = quiz_data['description']
        questions = quiz_data['questions']
        
        # Build questions HTML
        questions_html = ""
        for i, question in enumerate(questions):
            question_html = f"""
            <div class="question" data-question="{i}">
                <h3>Question {i + 1}</h3>
                <p class="question-text">{question['question']}</p>
                <div class="question-type">{question['type'].replace('_', ' ').title()}</div>
            """
            
            if question['type'] == 'multiple_choice':
                question_html += '<div class="options">'
                for j, option in enumerate(question['options']):
                    question_html += f'''
                    <label>
                        <input type="radio" name="q{i}" value="{option}">
                        {option}
                    </label>
                    '''
                question_html += '</div>'
            
            elif question['type'] == 'true_false':
                question_html += '''
                <div class="options">
                    <label><input type="radio" name="q{}" value="true"> True</label>
                    <label><input type="radio" name="q{}" value="false"> False</label>
                </div>
                '''.format(i, i)
            
            elif question['type'] == 'short_answer':
                question_html += f'<input type="text" name="q{i}" class="short-answer">'
            
            question_html += f'''
                <div class="question-meta">
                    <span class="section">Section: {question.get('section', 'General')}</span>
                    <span class="difficulty">Difficulty: {question.get('difficulty', 'medium')}</span>
                </div>
            </div>
            '''
            
            questions_html += question_html
        
        # Complete HTML
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }}
        .quiz-header {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .quiz-title {{
            color: #2563eb;
            margin-bottom: 10px;
        }}
        .quiz-description {{
            color: #666;
            margin-bottom: 20px;
        }}
        .quiz-meta {{
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #888;
        }}
        .question {{
            background: white;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .question h3 {{
            color: #2563eb;
            margin-bottom: 15px;
        }}
        .question-text {{
            font-size: 16px;
            margin-bottom: 15px;
            font-weight: 500;
        }}
        .question-type {{
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            margin-bottom: 15px;
        }}
        .options {{
            margin: 15px 0;
        }}
        .options label {{
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }}
        .options label:hover {{
            background: #e9ecef;
        }}
        .options input {{
            margin-right: 10px;
        }}
        .short-answer {{
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }}
        .question-meta {{
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #888;
        }}
        .question-meta span {{
            margin-right: 15px;
        }}
        .submit-btn {{
            background: #2563eb;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }}
        .submit-btn:hover {{
            background: #1d4ed8;
        }}
        .results {{
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }}
    </style>
</head>
<body>
    <div class="quiz-header">
        <h1 class="quiz-title">{title}</h1>
        <p class="quiz-description">{description}</p>
        <div class="quiz-meta">
            <span>Questions: {len(questions)}</span>
            <span>Estimated Time: {quiz_data['metadata']['estimated_time']} minutes</span>
            <span>Difficulty: {quiz_data['metadata']['difficulty']}</span>
        </div>
    </div>
    
    <form id="quiz-form">
        {questions_html}
        <button type="submit" class="submit-btn">Submit Quiz</button>
    </form>
    
    <div id="results" class="results">
        <h2>Quiz Results</h2>
        <div id="score"></div>
        <div id="feedback"></div>
    </div>
    
    <script>
        const quizData = {json.dumps(quiz_data)};
        
        document.getElementById('quiz-form').addEventListener('submit', function(e) {{
            e.preventDefault();
            
            let score = 0;
            let total = quizData.questions.length;
            let feedback = [];
            
            quizData.questions.forEach((question, index) => {{
                const userAnswer = getUserAnswer(index, question.type);
                const correct = checkAnswer(userAnswer, question);
                
                if (correct) {{
                    score++;
                    feedback.push(`Question ${{index + 1}}: Correct!`);
                }} else {{
                    feedback.push(`Question ${{index + 1}}: Incorrect. Correct answer: ${{question.correct_answer}}`);
                }}
            }});
            
            const percentage = Math.round((score / total) * 100);
            
            document.getElementById('score').innerHTML = `
                <h3>Your Score: ${{score}}/${{total}} (${{percentage}}%)</h3>
            `;
            
            document.getElementById('feedback').innerHTML = feedback.map(f => `<p>${{f}}</p>`).join('');
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({{ behavior: 'smooth' }});
        }});
        
        function getUserAnswer(questionIndex, questionType) {{
            if (questionType === 'multiple_choice' || questionType === 'true_false') {{
                const selected = document.querySelector(`input[name="q${{questionIndex}}"]:checked`);
                return selected ? selected.value : null;
            }} else if (questionType === 'short_answer') {{
                const input = document.querySelector(`input[name="q${{questionIndex}}"]`);
                return input ? input.value.trim() : null;
            }}
            return null;
        }}
        
        function checkAnswer(userAnswer, question) {{
            if (question.type === 'true_false') {{
                return userAnswer === question.correct_answer.toString();
            }}
            return userAnswer === question.correct_answer;
        }}
    </script>
</body>
</html>"""
        
        return html_content
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system usage"""
        import re
        filename = filename.replace(' ', '_')
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        return filename.lower()[:50] if filename else 'quiz'
