#!/usr/bin/env python3
"""
Test runner for DocuMorph

This script runs all tests and generates coverage reports.
"""

import sys
import subprocess
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False


def main():
    """Main test runner"""
    print("DocuMorph Test Runner")
    print("=" * 60)
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Check if we're in a virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("WARNING: Not running in a virtual environment")
        print("Consider activating a virtual environment first")
    
    # Install dependencies if needed
    print("\nInstalling dependencies...")
    if not run_command("pip install -e .", "Installing DocuMorph in development mode"):
        print("Failed to install dependencies")
        return 1
    
    # Run code quality checks
    print("\n" + "="*60)
    print("CODE QUALITY CHECKS")
    print("="*60)
    
    # Check if flake8 is available
    try:
        subprocess.run(["flake8", "--version"], check=True, capture_output=True)
        run_command("flake8 documorph/ --max-line-length=100 --ignore=E203,W503", "Code style check (flake8)")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("flake8 not available, skipping code style check")
    
    # Check if black is available
    try:
        subprocess.run(["black", "--version"], check=True, capture_output=True)
        run_command("black --check documorph/", "Code formatting check (black)")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("black not available, skipping code formatting check")
    
    # Run unit tests
    print("\n" + "="*60)
    print("UNIT TESTS")
    print("="*60)
    
    test_commands = [
        ("python -m pytest tests/ -v", "Running all unit tests"),
        ("python -m pytest tests/test_parsers.py -v", "Testing document parsers"),
        ("python -m pytest tests/test_generators.py -v", "Testing output generators"),
        ("python -m pytest tests/test_cli.py -v", "Testing CLI interface"),
    ]
    
    test_results = []
    for command, description in test_commands:
        result = run_command(command, description)
        test_results.append((description, result))
    
    # Run coverage analysis
    print("\n" + "="*60)
    print("COVERAGE ANALYSIS")
    print("="*60)
    
    coverage_commands = [
        ("python -m pytest tests/ --cov=documorph --cov-report=term-missing", "Coverage report"),
        ("python -m pytest tests/ --cov=documorph --cov-report=html", "HTML coverage report"),
    ]
    
    for command, description in coverage_commands:
        try:
            run_command(command, description)
        except Exception:
            print(f"Coverage analysis failed - pytest-cov may not be installed")
    
    # Run integration tests
    print("\n" + "="*60)
    print("INTEGRATION TESTS")
    print("="*60)
    
    integration_commands = [
        ("python examples/basic_usage.py", "Basic usage examples"),
        ("documorph --help", "CLI help command"),
        ("documorph formats", "List supported formats"),
    ]
    
    for command, description in integration_commands:
        run_command(command, description)
    
    # Test with sample files
    print("\n" + "="*60)
    print("SAMPLE FILE TESTS")
    print("="*60)
    
    sample_tests = [
        ("documorph info tests/fixtures/sample.md", "Document info for sample Markdown"),
        ("documorph info tests/fixtures/sample.txt", "Document info for sample text"),
    ]
    
    for command, description in sample_tests:
        run_command(command, description)
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    print("\nUnit Test Results:")
    passed = 0
    failed = 0
    
    for description, result in test_results:
        status = "PASSED" if result else "FAILED"
        print(f"  {status}: {description}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {passed + failed} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed > 0:
        print(f"\n❌ {failed} test(s) failed")
        return 1
    else:
        print(f"\n✅ All tests passed!")
        return 0


if __name__ == "__main__":
    sys.exit(main())
