#!/usr/bin/env python3
"""
Logger configuration for DocuMorph

This module provides logging setup and utilities.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
from config.config_manager import config_manager


def setup_logger(name: str = "documorph", 
                log_file: Optional[str] = None,
                log_level: str = "INFO",
                console_output: bool = True) -> logging.Logger:
    """
    Set up logger with file and console handlers
    
    Args:
        name: Logger name
        log_file: Optional log file path
        log_level: Logging level
        console_output: Whether to output to console
        
    Returns:
        Configured logger
    """
    # Get configuration
    if not log_file:
        log_file = config_manager.get('logging.file', 'logs/documorph.log')
    
    if not log_level:
        log_level = config_manager.get('logging.level', 'INFO')
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        config_manager.get('logging.format', 
                          '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )
    
    # File handler
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Use rotating file handler
        max_size = config_manager.get('logging.max_size', '10MB')
        backup_count = config_manager.get('logging.backup_count', 5)
        
        max_bytes = _parse_size_string(max_size)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger


def _parse_size_string(size_str: str) -> int:
    """
    Parse size string to bytes
    
    Args:
        size_str: Size string (e.g., "10MB")
        
    Returns:
        Size in bytes
    """
    import re
    
    size_str = size_str.upper().strip()
    match = re.match(r'^(\d+(?:\.\d+)?)\s*([KMGT]?B?)$', size_str)
    
    if not match:
        return 10 * 1024 * 1024  # Default 10MB
    
    number = float(match.group(1))
    unit = match.group(2) or 'B'
    
    multipliers = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 ** 2,
        'GB': 1024 ** 3,
        'TB': 1024 ** 4
    }
    
    return int(number * multipliers.get(unit, 1))


# Create default logger
default_logger = setup_logger()
