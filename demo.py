#!/usr/bin/env python3
"""
DocuMorph Complete Demonstration

This script demonstrates all the key features of DocuMorph in a comprehensive demo.
"""

import os
import sys
import time
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich import print as rprint

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from documorph.core.document_parser import DocumentParser
    from documorph.core.content_processor import ContentProcessor
    from documorph.core.output_generator import OutputGenerator
    from documorph.utils.file_handler import FileHandler
    from documorph.utils.validators import FileValidator
    from config.config_manager import config_manager
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure DocuMorph is properly installed:")
    print("  pip install -e .")
    sys.exit(1)

console = Console()


def print_header():
    """Print demo header"""
    header_text = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                     DocuMorph Demo                          ║
    ║              Document Transformation Tool                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    console.print(header_text, style="bold blue")


def create_demo_documents():
    """Create sample documents for demonstration"""
    console.print("\n📄 Creating Demo Documents...", style="bold yellow")
    
    demo_dir = Path("demo_documents")
    demo_dir.mkdir(exist_ok=True)
    
    documents = {
        "technical_guide.md": """# Technical Implementation Guide

## Overview
This guide covers the technical implementation of our new system architecture.

## System Requirements
- Python 3.8+
- 4GB RAM minimum
- 50GB storage space
- Network connectivity

## Installation Process

### Step 1: Environment Setup
First, prepare your development environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\\Scripts\\activate
pip install -r requirements.txt
```

### Step 2: Configuration
Configure the system settings in `config.yaml`:

```yaml
database:
  host: localhost
  port: 5432
  name: production_db
```

### Step 3: Deployment
Deploy using the automated script:

```bash
./deploy.sh production
```

## Architecture Overview
The system follows a microservices architecture with the following components:
- API Gateway
- Authentication Service
- Data Processing Engine
- Notification System

## Performance Metrics
Expected performance characteristics:
- Response time: < 200ms
- Throughput: 1000 requests/second
- Availability: 99.9%

## Troubleshooting
Common issues and solutions:

1. **Connection Timeout**: Check network configuration
2. **Memory Issues**: Increase heap size
3. **Performance Degradation**: Review query optimization

## Conclusion
This implementation provides a robust, scalable solution for our requirements.
""",
        
        "user_manual.txt": """USER MANUAL

GETTING STARTED

Welcome to our application! This manual will help you get started quickly.

SYSTEM OVERVIEW

Our application is designed to streamline document processing workflows.
Key features include:
- Document parsing and analysis
- Multi-format output generation
- Batch processing capabilities
- Integration with external services

INSTALLATION

1. Download the installer from our website
2. Run the installation wizard
3. Follow the on-screen instructions
4. Restart your computer when prompted

BASIC USAGE

Creating Your First Project:
1. Click "New Project" in the main menu
2. Select your input documents
3. Choose output formats
4. Configure settings as needed
5. Click "Process" to begin

ADVANCED FEATURES

Batch Processing:
- Select multiple files using Ctrl+Click
- Choose "Batch Process" from the File menu
- Configure global settings
- Monitor progress in the status window

Custom Templates:
- Access the Template Manager
- Create new templates or modify existing ones
- Save templates for future use

TROUBLESHOOTING

Problem: Application won't start
Solution: Check system requirements and reinstall if necessary

Problem: Processing fails
Solution: Verify input file formats and check error logs

Problem: Slow performance
Solution: Close other applications and check available memory

SUPPORT

For additional help:
- Visit our online documentation
- Contact technical support
- Join our user community forum

Thank you for using our application!
""",
        
        "project_report.md": """# Q4 Project Report

## Executive Summary
This report summarizes the achievements and outcomes of our Q4 initiatives.

## Project Objectives
- Improve system performance by 30%
- Reduce processing time by 50%
- Enhance user experience
- Implement new security features

## Methodology
We employed an agile development approach with:
- Weekly sprint cycles
- Continuous integration/deployment
- Regular stakeholder feedback
- Performance monitoring

## Key Results

### Performance Improvements
- **Response Time**: Reduced from 500ms to 150ms (70% improvement)
- **Throughput**: Increased from 500 to 1200 requests/second (140% improvement)
- **Error Rate**: Decreased from 2.1% to 0.3% (86% improvement)

### User Experience Enhancements
- Redesigned user interface
- Improved navigation flow
- Added accessibility features
- Mobile responsiveness

### Security Upgrades
- Implemented OAuth 2.0 authentication
- Added encryption for data at rest
- Enhanced audit logging
- Regular security scans

## Challenges and Solutions

### Challenge 1: Legacy System Integration
**Problem**: Difficulty integrating with legacy systems
**Solution**: Developed custom adapters and migration tools

### Challenge 2: Performance Bottlenecks
**Problem**: Database queries causing slowdowns
**Solution**: Optimized queries and implemented caching

### Challenge 3: User Adoption
**Problem**: Slow user adoption of new features
**Solution**: Enhanced training and documentation

## Financial Impact
- Development cost: $150,000
- Expected annual savings: $300,000
- ROI: 200% within first year

## Recommendations
1. Continue performance optimization efforts
2. Expand security features
3. Develop mobile application
4. Implement advanced analytics

## Conclusion
The Q4 project exceeded expectations and delivered significant value to the organization.
"""
    }
    
    created_files = []
    for filename, content in documents.items():
        file_path = demo_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        created_files.append(file_path)
        console.print(f"  ✅ Created: {filename}", style="green")
    
    return demo_dir, created_files


def demonstrate_parsing():
    """Demonstrate document parsing capabilities"""
    console.print("\n🔍 Document Parsing Demo", style="bold yellow")
    
    demo_dir, demo_files = create_demo_documents()
    parser = DocumentParser()
    
    # Create a table to show parsing results
    table = Table(title="Document Parsing Results")
    table.add_column("File", style="cyan")
    table.add_column("Format", style="magenta")
    table.add_column("Size", style="green")
    table.add_column("Words", style="yellow")
    table.add_column("Status", style="bold")
    
    for file_path in demo_files:
        try:
            parsed_doc = parser.parse(str(file_path))
            
            file_size = file_path.stat().st_size
            word_count = parsed_doc.metadata.get('word_count', 0)
            
            table.add_row(
                file_path.name,
                parsed_doc.format.upper(),
                f"{file_size:,} bytes",
                f"{word_count:,} words",
                "✅ Success"
            )
            
        except Exception as e:
            table.add_row(
                file_path.name,
                "Unknown",
                "N/A",
                "N/A",
                f"❌ Error: {str(e)[:30]}..."
            )
    
    console.print(table)
    return demo_dir, demo_files


def demonstrate_processing():
    """Demonstrate content processing"""
    console.print("\n⚙️ Content Processing Demo", style="bold yellow")
    
    demo_dir = Path("demo_documents")
    demo_files = list(demo_dir.glob("*"))
    
    parser = DocumentParser()
    processor = ContentProcessor()
    
    processing_results = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        task = progress.add_task("Processing documents...", total=len(demo_files))
        
        for file_path in demo_files:
            try:
                # Parse document
                parsed_doc = parser.parse(str(file_path))
                
                # Process content
                processed_content = processor.process(parsed_doc)
                
                processing_results.append({
                    'file': file_path.name,
                    'sections': len(processed_content.sections),
                    'reading_time': processed_content.reading_time,
                    'difficulty': processed_content.difficulty_level,
                    'keywords': len(processed_content.keywords),
                    'summary_length': len(processed_content.summary)
                })
                
                progress.advance(task)
                
            except Exception as e:
                console.print(f"❌ Error processing {file_path.name}: {e}")
                progress.advance(task)
    
    # Display processing results
    table = Table(title="Content Processing Results")
    table.add_column("Document", style="cyan")
    table.add_column("Sections", style="magenta")
    table.add_column("Reading Time", style="green")
    table.add_column("Difficulty", style="yellow")
    table.add_column("Keywords", style="blue")
    
    for result in processing_results:
        table.add_row(
            result['file'],
            str(result['sections']),
            f"{result['reading_time']} min",
            result['difficulty'],
            str(result['keywords'])
        )
    
    console.print(table)
    return processing_results


def demonstrate_output_generation():
    """Demonstrate output generation"""
    console.print("\n🎯 Output Generation Demo", style="bold yellow")
    
    demo_dir = Path("demo_documents")
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    parser = DocumentParser()
    processor = ContentProcessor()
    generator = OutputGenerator()
    
    # Process one file for each output format
    sample_file = demo_dir / "technical_guide.md"
    
    if not sample_file.exists():
        console.print("❌ Sample file not found", style="red")
        return
    
    try:
        # Parse and process
        parsed_doc = parser.parse(str(sample_file))
        processed_content = processor.process(parsed_doc)
        
        # Generate different outputs
        output_formats = ['html', 'quiz']  # Limiting to formats that don't require external APIs
        
        generation_results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            task = progress.add_task("Generating outputs...", total=len(output_formats))
            
            for output_format in output_formats:
                try:
                    output_path = generator.generate(
                        processed_content,
                        output_format,
                        str(output_dir)
                    )
                    
                    output_file = Path(output_path)
                    file_size = output_file.stat().st_size if output_file.exists() else 0
                    
                    generation_results.append({
                        'format': output_format.upper(),
                        'file': output_file.name if output_file.exists() else 'N/A',
                        'size': f"{file_size:,} bytes" if file_size > 0 else 'N/A',
                        'status': '✅ Success' if output_file.exists() else '❌ Failed'
                    })
                    
                    progress.advance(task)
                    
                except Exception as e:
                    generation_results.append({
                        'format': output_format.upper(),
                        'file': 'N/A',
                        'size': 'N/A',
                        'status': f'❌ Error: {str(e)[:30]}...'
                    })
                    progress.advance(task)
        
        # Display generation results
        table = Table(title="Output Generation Results")
        table.add_column("Format", style="cyan")
        table.add_column("Output File", style="magenta")
        table.add_column("File Size", style="green")
        table.add_column("Status", style="bold")
        
        for result in generation_results:
            table.add_row(
                result['format'],
                result['file'],
                result['size'],
                result['status']
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"❌ Error in output generation: {e}", style="red")


def demonstrate_configuration():
    """Demonstrate configuration system"""
    console.print("\n⚙️ Configuration Demo", style="bold yellow")
    
    # Show current configuration
    config_info = [
        ("Max File Size", config_manager.get('processing.max_file_size', 'N/A')),
        ("Supported Formats", ', '.join(config_manager.get('processing.supported_formats', []))),
        ("HTML Theme", config_manager.get('output.html.theme', 'N/A')),
        ("Quiz Question Types", ', '.join(config_manager.get('output.quiz.question_types', []))),
        ("Log Level", config_manager.get('logging.level', 'N/A')),
    ]
    
    table = Table(title="Current Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    for setting, value in config_info:
        table.add_row(setting, str(value))
    
    console.print(table)


def cleanup_demo():
    """Clean up demo files"""
    console.print("\n🧹 Cleaning Up Demo Files...", style="bold yellow")
    
    import shutil
    
    cleanup_dirs = ["demo_documents", "demo_output"]
    
    for dir_name in cleanup_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            console.print(f"  ✅ Removed: {dir_name}", style="green")


def main():
    """Main demo function"""
    print_header()
    
    console.print("\n🚀 Starting DocuMorph Complete Demonstration", style="bold green")
    console.print("This demo will showcase all key features of DocuMorph.\n")
    
    try:
        # Run demonstrations
        demonstrate_parsing()
        time.sleep(1)
        
        demonstrate_processing()
        time.sleep(1)
        
        demonstrate_output_generation()
        time.sleep(1)
        
        demonstrate_configuration()
        time.sleep(1)
        
        # Summary
        console.print("\n" + "="*60, style="bold blue")
        console.print("🎉 Demo Complete! DocuMorph Features Demonstrated:", style="bold green")
        console.print("  ✅ Document parsing (MD, TXT formats)")
        console.print("  ✅ Content processing and analysis")
        console.print("  ✅ Output generation (HTML, Quiz)")
        console.print("  ✅ Configuration management")
        console.print("  ✅ Error handling and validation")
        
        console.print("\n📚 Next Steps:", style="bold yellow")
        console.print("  • Try the CLI: documorph --help")
        console.print("  • Read the docs: docs/usage.md")
        console.print("  • Run tests: python run_tests.py")
        console.print("  • Explore examples: examples/")
        
        console.print("\n🌟 DocuMorph is ready for document transformation!", style="bold green")
        
    except KeyboardInterrupt:
        console.print("\n⚠️ Demo interrupted by user", style="yellow")
    except Exception as e:
        console.print(f"\n❌ Demo error: {e}", style="red")
        import traceback
        traceback.print_exc()
    finally:
        cleanup_demo()


if __name__ == "__main__":
    main()
