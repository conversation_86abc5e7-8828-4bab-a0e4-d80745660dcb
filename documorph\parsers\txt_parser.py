#!/usr/bin/env python3
"""
TXT Parser for DocuMorph

This module handles parsing of plain text documents.
"""

import logging
from typing import Dict, Any, Tuple, List
from pathlib import Path
import re
import chardet

logger = logging.getLogger(__name__)


class TXTParser:
    """
    Parser for plain text documents
    """
    
    description = "Plain text document parser with encoding detection"
    
    def __init__(self):
        """Initialize the TXT parser"""
        self.supported_encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252', 'ascii']
        logger.info("TXTParser initialized")
    
    def parse(self, file_path: str, **kwargs) -> Tuple[str, Dict[str, Any], Dict[str, Any]]:
        """
        Parse a plain text document
        
        Args:
            file_path: Path to the text file
            **kwargs: Additional parsing options
                - encoding: str - Force specific encoding (default: auto-detect)
                - preserve_whitespace: bool - Preserve original whitespace (default: False)
                - detect_structure: bool - Attempt to detect document structure (default: True)
        
        Returns:
            Tuple of (content, metadata, structure)
        """
        try:
            encoding = kwargs.get('encoding', None)
            preserve_whitespace = kwargs.get('preserve_whitespace', False)
            detect_structure = kwargs.get('detect_structure', True)
            
            logger.info(f"Parsing TXT: {file_path}")
            
            # Detect encoding if not specified
            if not encoding:
                encoding = self._detect_encoding(file_path)
            
            # Read file content
            with open(file_path, 'r', encoding=encoding, errors='replace') as file:
                content = file.read()
            
            # Extract metadata
            metadata = self._extract_metadata(file_path, content, encoding)
            
            # Process content
            if not preserve_whitespace:
                content = self._normalize_whitespace(content)
            
            # Extract structure
            structure = {}
            if detect_structure:
                structure = self._analyze_structure(content)
            
            logger.info(f"Successfully parsed TXT with {len(content)} characters")
            return content, metadata, structure
            
        except Exception as e:
            logger.error(f"Error parsing TXT {file_path}: {e}")
            raise
    
    def _detect_encoding(self, file_path: str) -> str:
        """
        Detect file encoding
        
        Args:
            file_path: Path to the text file
            
        Returns:
            Detected encoding string
        """
        try:
            with open(file_path, 'rb') as file:
                raw_data = file.read(10000)  # Read first 10KB for detection
            
            # Use chardet for encoding detection
            result = chardet.detect(raw_data)
            detected_encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)
            
            logger.info(f"Detected encoding: {detected_encoding} (confidence: {confidence:.2f})")
            
            # Fallback to utf-8 if confidence is too low
            if confidence < 0.7:
                logger.warning(f"Low confidence in encoding detection, using utf-8")
                return 'utf-8'
            
            return detected_encoding
            
        except Exception as e:
            logger.warning(f"Error detecting encoding: {e}, using utf-8")
            return 'utf-8'
    
    def _extract_metadata(self, file_path: str, content: str, encoding: str) -> Dict[str, Any]:
        """
        Extract metadata from text file
        
        Args:
            file_path: Path to the text file
            content: File content
            encoding: File encoding
            
        Returns:
            Dictionary containing metadata
        """
        file_path_obj = Path(file_path)
        lines = content.split('\n')
        
        metadata = {
            'file_path': file_path,
            'file_name': file_path_obj.name,
            'format': 'txt',
            'encoding': encoding,
            'size_bytes': file_path_obj.stat().st_size,
            'line_count': len(lines),
            'word_count': len(content.split()),
            'char_count': len(content),
            'char_count_no_spaces': len(content.replace(' ', '')),
            'paragraph_count': len([p for p in content.split('\n\n') if p.strip()]),
            'created': str(file_path_obj.stat().st_ctime),
            'modified': str(file_path_obj.stat().st_mtime)
        }
        
        # Analyze content characteristics
        metadata.update(self._analyze_content_characteristics(content))
        
        return metadata
    
    def _analyze_content_characteristics(self, content: str) -> Dict[str, Any]:
        """
        Analyze characteristics of the text content
        
        Args:
            content: Text content
            
        Returns:
            Dictionary with content characteristics
        """
        lines = content.split('\n')
        words = content.split()
        
        # Calculate statistics
        non_empty_lines = [line for line in lines if line.strip()]
        
        characteristics = {
            'empty_lines': len(lines) - len(non_empty_lines),
            'avg_line_length': sum(len(line) for line in non_empty_lines) / len(non_empty_lines) if non_empty_lines else 0,
            'avg_word_length': sum(len(word) for word in words) / len(words) if words else 0,
            'max_line_length': max(len(line) for line in lines) if lines else 0,
            'has_tabs': '\t' in content,
            'has_special_chars': bool(re.search(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\]', content)),
            'language_hints': self._detect_language_hints(content)
        }
        
        return characteristics
    
    def _detect_language_hints(self, content: str) -> Dict[str, Any]:
        """
        Detect language hints from content
        
        Args:
            content: Text content
            
        Returns:
            Dictionary with language hints
        """
        hints = {
            'likely_code': False,
            'likely_markup': False,
            'likely_data': False,
            'likely_prose': False
        }
        
        # Check for code patterns
        code_patterns = [
            r'def\s+\w+\s*\(',  # Python function
            r'function\s+\w+\s*\(',  # JavaScript function
            r'class\s+\w+\s*[{:]',  # Class definition
            r'import\s+\w+',  # Import statement
            r'#include\s*<',  # C/C++ include
            r'<\?php',  # PHP tag
        ]
        
        if any(re.search(pattern, content) for pattern in code_patterns):
            hints['likely_code'] = True
        
        # Check for markup patterns
        markup_patterns = [
            r'<[^>]+>',  # HTML/XML tags
            r'\[.*?\]\(.*?\)',  # Markdown links
            r'#{1,6}\s+',  # Markdown headers
            r'\*\*.*?\*\*',  # Bold text
        ]
        
        if any(re.search(pattern, content) for pattern in markup_patterns):
            hints['likely_markup'] = True
        
        # Check for data patterns
        data_patterns = [
            r'^\s*\w+[,\t]\w+',  # CSV-like
            r'^\s*[\{\[]',  # JSON-like
            r'^\s*\w+:\s*\w+',  # Key-value pairs
        ]
        
        if any(re.search(pattern, content, re.MULTILINE) for pattern in data_patterns):
            hints['likely_data'] = True
        
        # Check for prose patterns
        prose_indicators = [
            len(re.findall(r'[.!?]', content)) > 10,  # Many sentences
            len(content.split()) > 100,  # Many words
            not hints['likely_code'] and not hints['likely_data']
        ]
        
        if all(prose_indicators):
            hints['likely_prose'] = True
        
        return hints
    
    def _normalize_whitespace(self, content: str) -> str:
        """
        Normalize whitespace in content
        
        Args:
            content: Original content
            
        Returns:
            Content with normalized whitespace
        """
        # Replace multiple spaces with single space
        content = re.sub(r' +', ' ', content)
        
        # Replace multiple newlines with double newline
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        
        # Remove trailing whitespace from lines
        lines = content.split('\n')
        lines = [line.rstrip() for line in lines]
        content = '\n'.join(lines)
        
        return content.strip()
    
    def _analyze_structure(self, content: str) -> Dict[str, Any]:
        """
        Analyze document structure
        
        Args:
            content: Text content
            
        Returns:
            Dictionary with structure information
        """
        structure = {
            'sections': [],
            'headings': [],
            'lists': [],
            'paragraphs': [],
            'code_blocks': [],
            'quotes': []
        }
        
        lines = content.split('\n')
        current_section = None
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if not line_stripped:
                continue
            
            # Detect headings (lines that are short, capitalized, or followed by underlines)
            if self._is_likely_heading(line, lines, i):
                heading = {
                    'text': line_stripped,
                    'line_number': i + 1,
                    'level': self._estimate_heading_level(line, lines, i)
                }
                structure['headings'].append(heading)
                
                # Start new section
                current_section = {
                    'title': line_stripped,
                    'start_line': i + 1,
                    'content_lines': []
                }
                structure['sections'].append(current_section)
            
            # Detect lists
            elif self._is_list_item(line):
                list_item = {
                    'text': line_stripped,
                    'line_number': i + 1,
                    'type': 'bullet' if line_stripped.startswith(('-', '*', '•')) else 'numbered'
                }
                structure['lists'].append(list_item)
            
            # Detect code blocks (indented lines)
            elif line.startswith('    ') or line.startswith('\t'):
                code_block = {
                    'text': line,
                    'line_number': i + 1,
                    'indentation': len(line) - len(line.lstrip())
                }
                structure['code_blocks'].append(code_block)
            
            # Detect quotes (lines starting with >)
            elif line_stripped.startswith('>'):
                quote = {
                    'text': line_stripped[1:].strip(),
                    'line_number': i + 1
                }
                structure['quotes'].append(quote)
            
            # Regular paragraph
            else:
                paragraph = {
                    'text': line_stripped,
                    'line_number': i + 1,
                    'word_count': len(line_stripped.split())
                }
                structure['paragraphs'].append(paragraph)
            
            # Add to current section
            if current_section:
                current_section['content_lines'].append(i + 1)
        
        return structure
    
    def _is_likely_heading(self, line: str, lines: List[str], index: int) -> bool:
        """
        Check if a line is likely a heading
        
        Args:
            line: Current line
            lines: All lines
            index: Current line index
            
        Returns:
            True if line is likely a heading
        """
        line_stripped = line.strip()
        
        # Empty line
        if not line_stripped:
            return False
        
        # Too long to be a heading
        if len(line_stripped) > 100:
            return False
        
        # Check if followed by underline (=== or ---)
        if (index + 1 < len(lines) and 
            lines[index + 1].strip() and
            all(c in '=-_' for c in lines[index + 1].strip())):
            return True
        
        # All caps and short
        if line_stripped.isupper() and len(line_stripped) < 50:
            return True
        
        # Starts with number pattern
        if re.match(r'^\d+\.?\s+[A-Z]', line_stripped):
            return True
        
        # Title case and short
        if (line_stripped.istitle() and 
            len(line_stripped) < 80 and
            not line_stripped.endswith('.') and
            len(line_stripped.split()) <= 10):
            return True
        
        return False
    
    def _estimate_heading_level(self, line: str, lines: List[str], index: int) -> int:
        """
        Estimate heading level
        
        Args:
            line: Current line
            lines: All lines
            index: Current line index
            
        Returns:
            Estimated heading level (1-6)
        """
        line_stripped = line.strip()
        
        # Check for underline style
        if (index + 1 < len(lines) and 
            lines[index + 1].strip()):
            underline = lines[index + 1].strip()
            if all(c == '=' for c in underline):
                return 1
            elif all(c == '-' for c in underline):
                return 2
        
        # Check for numbered headings
        if re.match(r'^\d+\.?\s+', line_stripped):
            return 2
        
        # All caps = level 1
        if line_stripped.isupper():
            return 1
        
        # Default to level 3
        return 3
    
    def _is_list_item(self, line: str) -> bool:
        """
        Check if line is a list item
        
        Args:
            line: Line to check
            
        Returns:
            True if line is a list item
        """
        line_stripped = line.strip()
        
        # Bullet points
        if re.match(r'^[-*•]\s+', line_stripped):
            return True
        
        # Numbered lists
        if re.match(r'^\d+[.)]\s+', line_stripped):
            return True
        
        # Lettered lists
        if re.match(r'^[a-zA-Z][.)]\s+', line_stripped):
            return True
        
        return False
