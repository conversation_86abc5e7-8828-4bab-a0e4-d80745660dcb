#!/usr/bin/env python3
"""
DocuMorph - A comprehensive document transformation CLI tool

This package provides functionality to convert documents (PDF, DOCX, TXT, MD)
into multiple output formats including responsive web pages, interactive quizzes,
audio podcasts, and multi-language content.
"""

__version__ = "1.0.0"
__author__ = "HectorTa1989"
__email__ = "<EMAIL>"
__description__ = "A comprehensive document transformation CLI tool"
__url__ = "https://github.com/HectorTa1989/DocuMorph_AugmentCode_GeminiCanvas"

# Import main components
from .core.document_parser import DocumentParser
from .core.content_processor import ContentProcessor
from .core.output_generator import OutputGenerator

__all__ = [
    'DocumentParser',
    'ContentProcessor', 
    'OutputGenerator',
    '__version__',
    '__author__',
    '__email__',
    '__description__',
    '__url__'
]
